export interface WorkoutChargeMonthData {
  month: number
  quantity: number
}

export interface WorkoutChargeYearData {
  [monthKey: string]: WorkoutChargeMonthData
}

export interface WorkoutChargeGroupData {
  group_id: string
  group_name: string
  unit_price: number
  years: {
    [year: string]: WorkoutChargeYearData
  }
}

export interface WorkoutChargeCategoryData {
  category_id: string
  category_name: string
  workouts: {
    [workoutKey: string]: WorkoutChargeGroupData
  }
}

export interface WorkoutChargeContent {
  [categoryKey: string]: WorkoutChargeCategoryData
}

// Legacy interface for UI compatibility
export interface WorkoutCustomerCharge {
  id: string
  customer_id: number
  division_id: number
  workout_customer_charge_content: WorkoutChargeContent
  created_at: string
  updated_at: string
}

// New flat structure for database
export interface WorkoutCustomerChargeFlat {
  id: string
  customer_id: number
  division_id: number | null
  category_id: string
  workout_id: string
  year: number
  month_1: number | null
  month_2: number | null
  month_3: number | null
  month_4: number | null
  month_5: number | null
  month_6: number | null
  month_7: number | null
  month_8: number | null
  month_9: number | null
  month_10: number | null
  month_11: number | null
  month_12: number | null
  created_at: string
  updated_at: string
}

export interface WorkoutCustomerChargeFormData {
  customer_id: number
  division_id: number
  workout_customer_charge_content: WorkoutChargeContent
}

export interface WorkoutCustomerChargeFilters {
  search?: string
  customer_id?: number
  division_id?: number
  cate_id?: string
}

export interface CategoryOption {
  id: string
  name: string
}

export interface CategoryWorkoutOption {
  id: string
  name: string
  unit_price: number
  category_id: string
  calculationType?: string // Added for UI logic
}

export interface CustomerOption {
  id: number
  name: string
  code: string
}

export interface DivisionOption {
  id: number
  name: string
  code: string
  customer_id: number
}
