<template>
  <div class="invoice-list-view">
    <div class="header-section">
      <h2>Warehouse Invoice List</h2>
      <div class="filter-section">
        <a-select
          v-model:value="selectedCustomer"
          placeholder="Select Customer"
          style="width: 200px; margin-right: 12px"
          @change="onCustomerChange"
          allow-clear
        >
          <a-select-option v-for="c in customers" :key="c.id" :value="c.id">{{
            c.name
          }}</a-select-option>
        </a-select>
        <a-select
          v-model:value="selectedDivision"
          placeholder="Select Division"
          style="width: 200px; margin-right: 12px"
          :disabled="!selectedCustomer"
          allow-clear
        >
          <a-select-option v-for="d in divisions" :key="d.id" :value="d.id">{{
            d.name
          }}</a-select-option>
        </a-select>
        <a-date-picker
          v-model:value="selectedMonth"
          picker="month"
          style="width: 180px; margin-right: 12px"
        />
        <a-button type="primary" @click="handleCreateInvoice">
          <template #icon><FileAddOutlined /></template>
          Create Invoice
        </a-button>
      </div>
    </div>
    <a-card>
      <a-table
        :columns="columns"
        :data-source="filteredInvoices"
        row-key="id"
        :loading="isLoading"
        bordered
        :scroll="{ x: 'max-content' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'updatedAt'">
            {{ formatDate(record.updatedAt) }}
          </template>
          <template v-else-if="column.dataIndex === 'totalAmount'">
            {{ formatCurrency(record.totalAmount) }}
          </template>
          <template v-else-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <ActionButtons
              :name="'Invoice'"
              :show-view="true"
              :show-edit="record.status !== 'APPROVED'"
              :show-delete="true"
              @view="handleView(record)"
              @edit="handleEdit(record)"
              @delete="handleDelete(record)"
            />
          </template>
          <template v-else>
            {{ record[column.dataIndex] }}
          </template>
        </template>
      </a-table>
    </a-card>
    <a-modal
      v-model:visible="confirmModal.visible"
      :title="confirmModal.title"
      @ok="confirmModal.onOk"
      @cancel="() => (confirmModal.visible = false)"
    >
      <p>{{ confirmModal.content }}</p>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'
import { useInvoiceService } from '../services/invoiceLocalService'
import { FileAddOutlined } from '@ant-design/icons-vue'
import type { Invoice } from '../types/invoice'
import type { Customer } from '../types/MasterDataTypes/Customer'
import type { CustomerDivision } from '../types/MasterDataTypes/Customer'
import { useRouter } from 'vue-router'
import ActionButtons from '../components/ActionButtons.vue'
import { message } from 'ant-design-vue'

const invoiceService = useInvoiceService()
const router = useRouter()
const isLoading = ref(false)
const customers = ref<Customer[]>([])
const divisions = ref<CustomerDivision[]>([])
const invoices = ref<Invoice[]>([])
const selectedCustomer = ref<number | null>(null)
const selectedDivision = ref<number | null>(null)
const selectedMonth = ref<any>(null)

const confirmModal = ref({
  visible: false,
  title: '',
  content: '',
  onOk: () => {},
})

const columns = [
  { title: 'Invoice Number', dataIndex: 'id', key: 'id', width: 150 },
  { title: 'Customer', dataIndex: 'customerName', key: 'customerName', width: 180 },
  { title: 'Division', dataIndex: 'divisionName', key: 'divisionName', width: 180 },
  { title: 'Issued Date', dataIndex: 'updatedAt', key: 'updatedAt', width: 120 },
  { title: 'Total', dataIndex: 'totalAmount', key: 'totalAmount', width: 120, align: 'right' },
  { title: 'Status', dataIndex: 'status', key: 'status', width: 100 },
  { title: 'Action', dataIndex: 'action', key: 'action', width: 80, align: 'center' },
]

const formatDate = (date: string | undefined) => {
  return date ? dayjs(date).format('DD/MM/YYYY') : ''
}
const formatCurrency = (amount: number | undefined) => {
  if (amount === undefined) return '$0.00'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}
const getStatusColor = (status: string) => {
  switch (status?.toUpperCase()) {
    case 'DRAFT':
      return 'gray'
    case 'PENDING':
      return 'processing'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'error'
    case 'PAID':
      return 'blue'
    case 'OVERDUE':
      return 'error'
    default:
      return 'default'
  }
}

const fetchData = async () => {
  isLoading.value = true
  try {
    customers.value = (await invoiceService.getAllCustomers?.()) || []
    invoices.value = await invoiceService.getAllInvoices()

    // Fetch divisions for all customers that have invoices
    const uniqueCustomerIds = [
      ...new Set(invoices.value.map((inv) => inv.customerId).filter(Boolean)),
    ]
    const allDivisions = await Promise.all(
      uniqueCustomerIds.map((customerId) =>
        invoiceService.getCustomerDivisions(customerId as number),
      ),
    )
    divisions.value = allDivisions.flat()
  } finally {
    isLoading.value = false
  }
}

const onCustomerChange = async (customerId: number | null) => {
  selectedDivision.value = null
  divisions.value = []

  if (customerId) {
    try {
      // Only update the filter divisions when customer changes
      const customerDivisions = await invoiceService.getCustomerDivisions(customerId)
      divisions.value = customerDivisions
    } catch (error) {
      console.error('Error fetching divisions:', error)
      message.error('Failed to load divisions')
    }
  }
}

const filteredInvoices = computed(() => {
  let result = invoices.value
  if (selectedCustomer.value) {
    result = result.filter((inv) => inv.customerId === selectedCustomer.value)
  }
  if (selectedDivision.value) {
    result = result.filter((inv) => inv.customerDivisionId === selectedDivision.value)
  }
  if (selectedMonth.value) {
    const ym = dayjs(selectedMonth.value).format('YYYYMM')
    result = result.filter((inv) => dayjs(inv.updatedAt).format('YYYYMM') === ym)
  }
  return result.map((inv) => ({
    ...inv,
    customerName: customers.value.find((c: Customer) => c.id === inv.customerId)?.name || '',
    divisionName:
      divisions.value.find((d: CustomerDivision) => d.id === inv.customerDivisionId)?.name || '',
  }))
})

const handleCreateInvoice = () => {
  const query: Record<string, string> = { mode: 'new' }
  if (selectedCustomer.value) {
    query.customerId = String(selectedCustomer.value)
  }
  if (selectedDivision.value) {
    query.divisionId = String(selectedDivision.value)
  }
  router.push({ path: '/invoice-detail', query })
}
const handleView = (record: Invoice) => {
  router.push({ path: '/invoice-detail', query: { id: record.id, mode: 'view' } })
}
const handleEdit = (record: Invoice) => {
  router.push({ path: '/invoice-detail', query: { id: record.id, mode: 'edit' } })
}
const handleDelete = (record: Invoice) => {
  confirmModal.value = {
    visible: true,
    title: 'Delete Invoice',
    content: `Are you sure you want to delete invoice #${record.id}?`,
    onOk: async () => {
      confirmModal.value.visible = false
      isLoading.value = true
      await invoiceService.deleteInvoice(record.id)
      await fetchData()
      isLoading.value = false
    },
  }
}
const handleApprove = (record: Invoice) => {
  confirmModal.value = {
    visible: true,
    title: 'Approve Invoice',
    content: `Approve invoice #${record.id}?`,
    onOk: async () => {
      confirmModal.value.visible = false
      isLoading.value = true
      await invoiceService.updateInvoice(record.id, { status: 'APPROVED' })
      await fetchData()
      isLoading.value = false
    },
  }
}
const handleDisapprove = (record: Invoice) => {
  confirmModal.value = {
    visible: true,
    title: 'Disapprove Invoice',
    content: `Disapprove invoice #${record.id}?`,
    onOk: async () => {
      confirmModal.value.visible = false
      isLoading.value = true
      await invoiceService.updateInvoice(record.id, { status: 'DRAFT' })
      await fetchData()
      isLoading.value = false
    },
  }
}

onMounted(fetchData)
</script>

<style scoped>
.invoice-list-view {
  padding: 24px;
}
.header-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}
.filter-section {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style>
