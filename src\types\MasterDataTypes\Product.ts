// src/types/MasterDataTypes/Product.ts
export interface ImportBatch {
  id: number
  importedAt: string // Or Date
  importedByUserId?: number
  sourceFileName?: string
  notes?: string
}

export interface ImportedProduct {
  id: number
  productCode: string
  productName?: string
  productPpcNum?: string
  productHrc1?: string
  productHrc3?: string
  productPcm3?: string
  customerId?: number
  importedAt: string // Or Date
  importBatchId: number
}
