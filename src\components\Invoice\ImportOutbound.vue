<script setup lang="ts">
import { ref, reactive } from 'vue'
import * as XLSX from 'xlsx'
import { theme, message } from 'ant-design-vue'
import { useSQLite } from '@IVC/hooks/useSQLite'

const { token } = theme.useToken()
const Colors = ref(token.value)

// Initialize SQLite hook
const { executeQuery, initialize } = useSQLite()

// Create a ref for the file input element
const fileInputRef = ref<HTMLInputElement | null>(null)

// Helper function to validate and format Excel dates
const formatExcelDate = (value: number): { formattedDate: string; month: number } | null => {
  try {
    if (typeof value !== 'number') {
      return null
    }

    const date = XLSX.SSF.parse_date_code(value)
    if (!date || !date.y || !date.m || !date.d) {
      return null
    }

    // Format as mm/dd/yyyy
    const formattedDate = `${date.m.toString().padStart(2, '0')}/${date.d.toString().padStart(2, '0')}/${date.y}`

    return {
      formattedDate,
      month: date.m,
    }
  } catch (error) {
    console.error('Error formatting Excel date:', error)
    return null
  }
}

// Define additional validation functions
const validateExcelData = (data: ExcelRowData[]): { isValid: boolean; errorMessage?: string } => {
  if (data.length === 0) {
    return { isValid: false, errorMessage: 'The file contains no records.' }
  }

  // Check for minimum required columns - customize based on requirements
  const requiredColumns = ['SPH_SHIP_YMD']
  const availableColumns = Object.keys(data[0]).filter((key) => key !== 'key')

  const missingColumns = requiredColumns.filter(
    (col) => !availableColumns.some((availCol) => availCol === col),
  )

  if (missingColumns.length > 0) {
    return {
      isValid: false,
      errorMessage: `Missing required columns: ${missingColumns.join(', ')}`,
    }
  }

  // Add any other business-specific validation rules here

  return { isValid: true }
}

// File upload state
const fileSelected = ref<File | null>(null)
const isDragging = ref(false)
const isLoading = ref(false)
const isImporting = ref(false)
const errorMessage = ref('')
const showError = ref(false)
const successMessage = ref('')
const showSuccess = ref(false)
const processingProgress = ref(0)
const showProgressBar = ref(false)
const uploadSuccess = ref(false) // Track if file was successfully uploaded

// Column mapping state
// Static database columns for outbound imports
const databaseColumns = ref<string[]>([
  'spr_as_num',
  'spr_asln_num',
  'spr_assq_num',
  'sph_ship_ymd',
  'sph_dlv_cod',
  'sph_dlv_nam1',
  'spr_prod_cod',
  'spd_prod_nam',
  'spr_rtpc_qty',
])
const columnMapping = ref<{ [key: string]: string }>({})
const showMapping = ref<boolean>(true)
const selectedExcelColumn = ref<string | null>(null)

// Save mapping dialog state
const showSaveMappingDialog = ref(false)

// Customer search state
const customers = ref<Customer[]>([])
const selectedCustomerId = ref<string | null>(null)

// Division search state
const divisions = ref<Division[]>([])
const selectedDivisionId = ref<string | null>(null)

// Month picker state
const selectedMonth = ref<string | null>(null)

// Customer interface - simplified to match customers table schema
interface Customer {
  id: string
  name: string
}

// Division interface
interface Division {
  id: string
  name: string
  code: string
}

// Handle month selection
const handleMonthChange = (date: string | null) => {
  selectedMonth.value = date
  console.log('Selected month:', date)
}

// Handle customer selection
const handleCustomerChange = (value: string | null) => {
  selectedCustomerId.value = value
  selectedDivisionId.value = null // Reset division when customer changes
  const selectedCustomer = customers.value.find((c) => c.id === value)
  if (selectedCustomer) {
    console.log('Selected customer:', selectedCustomer)
    loadDivisions(value) // Load divisions for selected customer
  } else {
    divisions.value = [] // Clear divisions when no customer selected
  }
}

// Load customers from database
const loadCustomers = async () => {
  try {
    const result = await executeQuery('SELECT id, name FROM customers')
    customers.value =
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        name: String(row[1]),
      })) || []
  } catch (error) {
    console.error('Error loading customers:', error)
    message.error('Failed to load customers')
  }
}

// Filter function for customer dropdown
interface SelectOption {
  value: string
  label?: string
}

const filterCustomerOption = (input: string, option: SelectOption) => {
  const customer = customers.value.find((c) => c.id === option.value)
  if (!customer) return false

  return customer.name.toLowerCase().includes(input.toLowerCase())
}

// Load divisions for selected customer
const loadDivisions = async (customerId: string | null) => {
  if (!customerId) {
    divisions.value = []
    return
  }
  try {
    const result = await executeQuery(
      'SELECT id, code, name FROM customer_divisions WHERE customer_id = ? AND status = ?',
      [customerId, 'active'],
    )
    divisions.value =
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        code: String(row[1]),
        name: String(row[2]),
      })) || []
  } catch (error) {
    console.error('Error loading divisions:', error)
    message.error('Failed to load divisions')
  }
}

// Filter function for division select
const filterDivisionOption = (input: string, option: { children?: string }) => {
  return option.children?.toLowerCase().includes(input.toLowerCase())
}

// Function to select an Excel column for mapping
const selectExcelColumn = (columnName: string) => {
  selectedExcelColumn.value = columnName
}

// Function to update column mapping
const updateColumnMapping = (dbColumn: string) => {
  if (selectedExcelColumn.value) {
    columnMapping.value[selectedExcelColumn.value] = dbColumn
    selectedExcelColumn.value = null // Reset selection after mapping
  }
}

// Function to clear one mapping
const clearMapping = (excelColumn: string) => {
  delete columnMapping.value[excelColumn]
}

// Function to clear all mappings
const clearAllMappings = () => {
  columnMapping.value = {}
}

// Save mapping functions
const saveMappingWithConfirm = () => {
  showSaveMappingDialog.value = true
}

const saveMapping = async () => {
  try {
    if (!selectedCustomerId.value) {
      message.error('Please select a customer before saving mapping')
      return
    }

    await initialize()

    // Delete existing mappings for this customer and import type
    await executeQuery(
      `
      DELETE FROM customer_import_mapping 
      WHERE customer_id = ? AND import_type = 'outbound'
    `,
      [selectedCustomerId.value],
    )

    // Insert new mappings
    const insertPromises = Object.entries(columnMapping.value).map(([excelColumn, dbColumn]) => {
      return executeQuery(
        `
        INSERT INTO customer_import_mapping (customer_id, import_type, excel_column, db_column)
        VALUES (?, 'outbound', ?, ?)
      `,
        [selectedCustomerId.value, excelColumn, dbColumn],
      )
    })

    await Promise.all(insertPromises)

    showSaveMappingDialog.value = false
    message.success('Column mapping saved successfully!')
  } catch (error) {
    console.error('Error saving mapping:', error)
    message.error('Failed to save column mapping')
  }
}

const loadSavedMappings = async () => {
  try {
    if (!selectedCustomerId.value) return

    await initialize()

    const result = await executeQuery(
      `
      SELECT excel_column, db_column 
      FROM customer_import_mapping 
      WHERE customer_id = ? AND import_type = 'outbound'
    `,
      [selectedCustomerId.value],
    )

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const savedMappings: { [key: string]: string } = {}
      result.result.resultRows.forEach((row: unknown[]) => {
        const excelColumn = row[0] as string
        const dbColumn = row[1] as string
        savedMappings[excelColumn] = dbColumn
      })
      columnMapping.value = savedMappings
      console.log('Loaded saved column mappings for outbound:', savedMappings)
      message.success(`Loaded ${Object.keys(savedMappings).length} saved column mappings`)
    } else {
      // Clear any existing mappings if no saved mappings found
      columnMapping.value = {}
      console.log('No saved column mappings found for this customer')
    }
  } catch (error) {
    console.error('Error loading saved mappings:', error)
    message.error('Failed to load saved mappings')
  }
}

// Function to show success message and auto-hide after delay
const showSuccessMessage = (message: string) => {
  successMessage.value = message
  showSuccess.value = true
  // Auto-hide success message after 5 seconds
  setTimeout(() => {
    showSuccess.value = false
  }, 5000)
}

// Define types for our data structures
interface TableColumn {
  title: string
  dataIndex: string
  key: string
  width?: number
}

interface PaginationConfig {
  current: number
  pageSize: number
  total: number
  showSizeChanger: boolean
  pageSizeOptions: string[]
  showTotal: (total: number) => string
}

// Define a type for Excel row data
interface ExcelRowData {
  [key: string]: string | number | boolean | null
  key: number
}

// Data display state
const importedData = ref<ExcelRowData[]>([])
const columns = ref<TableColumn[]>([])
const pagination = reactive<PaginationConfig>({
  current: 1,
  pageSize: 20, // Changed to 20 as per requirements
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['20', '50', '100'],
  showTotal: (total: number) => `Total ${total} Items`,
})

// Handle file selection through browse button
const handleFileSelect = (event: Event) => {
  // Check if customer is selected
  if (!selectedCustomerId.value) {
    errorMessage.value = 'Error: Please select a customer before uploading.'
    showError.value = true
    return
  }

  // Check if month is selected
  if (!selectedMonth.value) {
    errorMessage.value = 'Error: Please select a month before uploading.'
    showError.value = true
    return
  }

  const input = event.target as HTMLInputElement
  if (input.files && input.files.length > 0) {
    fileSelected.value = input.files[0]
    errorMessage.value = ''
    showError.value = false
    uploadFile()
  }
}

// Handle drag & drop events
const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
}

const handleDragLeave = () => {
  isDragging.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  // Check if customer is selected
  if (!selectedCustomerId.value) {
    errorMessage.value = 'Error: Please select a customer before uploading.'
    showError.value = true
    return
  }

  // Check if month is selected
  if (!selectedMonth.value) {
    errorMessage.value = 'Error: Please select a month before uploading.'
    showError.value = true
    return
  }
  isDragging.value = false
  if (e.dataTransfer && e.dataTransfer.files.length > 0) {
    fileSelected.value = e.dataTransfer.files[0]
    errorMessage.value = ''
    showError.value = false
    uploadFile()
  }
}

// Reset file selection
const clearFile = () => {
  fileSelected.value = null
  importedData.value = []
  columns.value = []
  pagination.current = 1
  pagination.total = 0
  errorMessage.value = ''
  showError.value = false
  successMessage.value = ''
  showSuccess.value = false
  processingProgress.value = 0
  showProgressBar.value = false
  uploadSuccess.value = false // Reset upload success flag
  columnMapping.value = {} // Clear column mappings
  selectedExcelColumn.value = null // Clear selected column
  resetForm()
}

// No row limit for processing or display

// Upload the Excel file and display content
const uploadFile = async () => {
  if (!fileSelected.value) return

  isLoading.value = true
  errorMessage.value = ''
  showError.value = false

  // Validate file type and extension
  const fileName = fileSelected.value.name.toLowerCase()
  const fileType = fileSelected.value.type
  const validExcelTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel.sheet.macroEnabled.12',
    'text/csv',
  ]

  const hasValidExtension =
    fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileName.endsWith('.csv')

  const hasValidMimeType = validExcelTypes.includes(fileType)

  if (!hasValidExtension && !hasValidMimeType) {
    errorMessage.value = 'Error: Please upload a valid Excel file (.xlsx, .xls) or CSV file.'
    showError.value = true
    isLoading.value = false
    return
  }

  try {
    // Check file size before processing
    const fileSizeMB = fileSelected.value.size / (1024 * 1024)
    if (fileSizeMB > 50) {
      // Warn for files larger than 50MB
      console.warn(
        `Large file detected (${fileSizeMB.toFixed(2)}MB). Processing may take some time.`,
      )
    }

    // Process Excel file using XLSX library with optimizations for large files
    const reader = new FileReader()

    reader.onload = (e: ProgressEvent<FileReader>) => {
      try {
        if (!e.target?.result) {
          throw new Error('Failed to read file data')
        }

        const data = e.target.result

        // Use XLSX to read the file
        const workbook = XLSX.read(data, { type: 'array' })

        if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
          errorMessage.value = 'Error: Could not find any worksheet in the file.'
          showError.value = true
          isLoading.value = false
          return
        }

        // Get the first worksheet
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]

        if (!worksheet) {
          errorMessage.value = 'Error: Could not find any worksheet in the file.'
          showError.value = true
          isLoading.value = false
          return
        }

        // Convert to JSON with options for large files
        const options = {
          header: 1,
          defval: '',
          blankrows: false,
        }

        // Get all rows as arrays (header row and data rows)
        const allRows = XLSX.utils.sheet_to_json<(string | number | null)[]>(worksheet, options)

        if (!allRows || allRows.length <= 1) {
          // Check if there's at least a header row and one data row
          errorMessage.value = 'Error: The worksheet appears to be empty.'
          showError.value = true
          isLoading.value = false
          return
        }

        // Extract headers from the first row
        const headers = allRows[0].map((header) => header?.toString() || 'Column')

        // Convert data rows to objects with headers as keys
        const jsonData: ExcelRowData[] = []

        // Track the month values for SPH_SHIP_YMD to validate consistency
        let dateColumnIndex = -1
        const uniqueMonths = new Set<number>()

        // Find the index of the SPH_SHIP_YMD column if it exists
        headers.forEach((header, index) => {
          if (header === 'SPH_SHIP_YMD') {
            dateColumnIndex = index
          }
        })

        // Process data in batches to improve performance for large files
        const BATCH_SIZE = 1000 // Process 1000 rows at a time
        const totalRows = allRows.length

        // Function to process a batch of rows
        const processBatch = (startIndex: number, endIndex: number): void => {
          for (let i = startIndex; i < endIndex && i < totalRows; i++) {
            const row = allRows[i]
            // Skip empty rows
            if (row && row.some((cell) => cell !== '')) {
              const rowObject: ExcelRowData = { key: i - 1 }

              // Map each cell to its corresponding header
              headers.forEach((header, index) => {
                let value: string | number | boolean | null = row[index]

                // Check if this is a date column and the value is a number
                if (header === 'SPH_SHIP_YMD' && typeof value === 'number') {
                  // Convert Excel date number to JavaScript Date using our helper
                  const dateResult = formatExcelDate(value)

                  if (dateResult) {
                    // Track the month for validation
                    uniqueMonths.add(dateResult.month)

                    // Use the formatted date
                    value = dateResult.formattedDate
                  }
                }

                rowObject[header] = value
              })

              jsonData.push(rowObject)
            }
          }
        }

        // For large files, process data in batches to keep the UI responsive
        if (totalRows > 5000) {
          // Start processing from the second row (skip header)
          let currentIndex = 1
          let processedRows = 0

          // Function to process batches with yield to UI thread
          const processNextBatch = () => {
            const batchEnd = Math.min(currentIndex + BATCH_SIZE, totalRows)
            processBatch(currentIndex, batchEnd)
            processedRows += batchEnd - currentIndex
            currentIndex = batchEnd

            // Update progress bar to show progress
            const percentComplete = Math.round((processedRows / (totalRows - 1)) * 100)
            processingProgress.value = percentComplete
            if (processedRows % (BATCH_SIZE * 2) === 0) {
              console.log(`Processing large file: ${percentComplete}% complete`)
            }

            if (currentIndex < totalRows) {
              setTimeout(processNextBatch, 0) // Yield to UI thread
            } else {
              // All batches processed, continue with validation
              finishProcessing()
            }
          }

          // Start batch processing
          processNextBatch()
        } else {
          // For smaller files, process all at once
          processBatch(1, totalRows)
          finishProcessing()
        }

        // This function is called after all rows are processed
        function finishProcessing() {
          // Check if there are multiple months in SPH_SHIP_YMD column
          if (dateColumnIndex !== -1) {
            if (uniqueMonths.size === 0) {
              errorMessage.value = 'Error: SPH_SHIP_YMD column does not contain any valid dates.'
              showError.value = true
              isLoading.value = false
              return
            } else if (uniqueMonths.size > 1) {
              errorMessage.value =
                'Error: SPH_SHIP_YMD column contains different months. All dates must be in the same month.'
              showError.value = true
              isLoading.value = false
              return
            }
          } else if (columns.value.some((col) => col.title === 'SPH_SHIP_YMD')) {
            // If column exists but index wasn't found, it may be empty
            errorMessage.value =
              'Warning: SPH_SHIP_YMD column exists but appears to be empty or contains invalid data.'
            showError.value = true
          }

          if (jsonData.length === 0) {
            errorMessage.value = 'Error: The file contains no records. Please upload a valid file.'
            showError.value = true
            isLoading.value = false
            return
          }

          // Perform validation on the parsed data using our validation function
          const validationResult = validateExcelData(jsonData)
          if (!validationResult.isValid) {
            errorMessage.value = `Error: ${validationResult.errorMessage}`
            showError.value = true
            isLoading.value = false
            return
          }

          // Generate dynamic columns based on headers
          const dynamicColumns: TableColumn[] = headers.map((header) => ({
            title: header,
            dataIndex: header,
            key: header,
            // Add special styling for date columns
            className: header === 'SPH_SHIP_YMD' ? 'date-column' : undefined,
          }))

          // Update data state
          columns.value = dynamicColumns
          importedData.value = jsonData

          pagination.total = jsonData.length
          pagination.current = 1
          isLoading.value = false
          showProgressBar.value = false // Hide progress bar
          uploadSuccess.value = true // Mark upload as successful
          showMapping.value = true // Show column mapping interface

          // Reset any existing column mappings
          columnMapping.value = {}
          selectedExcelColumn.value = null

          // Show success message
          showSuccessMessage(`Successfully processed ${jsonData.length} records.`)

          // Load saved mappings when customer is selected
          if (uploadSuccess.value) {
            loadSavedMappings()
          }
        }
      } catch (error) {
        console.error('Error processing Excel data:', error)
        errorMessage.value = `Error processing Excel file: ${error}`
        showError.value = true
        isLoading.value = false
      }
    }

    reader.onerror = () => {
      errorMessage.value = 'Error reading file'
      showError.value = true
      isLoading.value = false
    }

    // Read Excel file as array buffer
    reader.readAsArrayBuffer(fileSelected.value)
  } catch (error) {
    console.error('Error uploading file:', error)
    errorMessage.value = `Error: ${error}`
    showError.value = true
    isLoading.value = false
  }
}

// Show confirmation dialog state
const showConfirmDialog = ref(false)

// Import and validate data
const importData = async () => {
  if (importedData.value.length === 0) {
    errorMessage.value = 'Error: The file contains no records. Please upload a valid file.'
    showError.value = true
    return
  }

  // Check if customer is selected
  if (!selectedCustomerId.value) {
    errorMessage.value = 'Error: Please select a customer before importing.'
    showError.value = true
    return
  }

  // Check if month is selected
  if (!selectedMonth.value) {
    errorMessage.value = 'Error: Please select a month before importing.'
    showError.value = true
    return
  }

  // Check if column mappings are set
  const mappingCount = Object.keys(columnMapping.value).length
  if (mappingCount === 0) {
    errorMessage.value =
      'Error: No column mappings have been defined. Please map at least one column before importing.'
    showError.value = true
    return
  }

  // Check if required columns are mapped (add your required columns here)
  const requiredDbColumns = ['spr_as_num', 'sph_ship_ymd', 'spr_prod_cod']
  const mappedDbColumns = Object.values(columnMapping.value)

  const missingRequiredColumns = requiredDbColumns.filter((col) => !mappedDbColumns.includes(col))

  if (missingRequiredColumns.length > 0) {
    errorMessage.value = `Error: Required columns not mapped: ${missingRequiredColumns.join(', ')}. Please map these columns before importing.`
    showError.value = true
    return
  }

  // Show confirmation dialog
  showConfirmDialog.value = true
}

// Helper function to calculate sum of total_carton for N_A_OUT_ALL_SUM_CARTON
const calculateSumTotalCarton = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    // Build query based on whether division is selected
    let query = `
      SELECT COALESCE(SUM(total_carton), 0) as sum_total_carton
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
    `

    const params = [customerId, yearMonth]

    // Add division filter if specified
    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_carton query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalCarton = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_carton result: ${sumTotalCarton}`)
      return sumTotalCarton
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_carton:', error)
    throw error
  }
}

// Helper function to calculate sum of total_pcs for N_A_OUT_ALL_SUM_PCS
const calculateSumTotalPcs = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    // Build query based on whether division is selected
    let query = `
      SELECT COALESCE(SUM(total_pcs), 0) as sum_total_pcs
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
    `

    const params = [customerId, yearMonth]

    // Add division filter if specified
    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_pcs query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalPcs = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_pcs result: ${sumTotalPcs}`)
      return sumTotalPcs
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_pcs:', error)
    throw error
  }
}

// Helper function to calculate sum of total_m3 for N_A_OUT_ALL_SUM_M3
const calculateSumTotalM3 = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    // Build query based on whether division is selected
    let query = `
      SELECT COALESCE(SUM(total_m3), 0) as sum_total_m3
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
    `

    const params = [customerId, yearMonth]

    // Add division filter if specified
    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_m3 query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalM3 = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_m3 result: ${sumTotalM3}`)
      return sumTotalM3
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_m3:', error)
    throw error
  }
}

// Helper function to calculate sum of total_carton for Store (N_A_OUT_STORE_SUM_CARTON)
const calculateSumTotalCartonStore = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    // First, get COD codes from cod table with code_type 'Store'
    const codQuery = `
      SELECT code 
      FROM cod 
      WHERE code_type = 'Store'
    `

    console.log('Executing COD query:', codQuery)
    const codResult = await executeQuery(codQuery, [])

    if (!codResult?.result?.resultRows || codResult.result.resultRows.length === 0) {
      console.log('No Store codes found, returning 0')
      return 0
    }

    // Extract codes from result
    const storeCodes = codResult.result.resultRows.map((row: unknown[]) => row[0] as string)
    console.log('Found Store codes:', storeCodes)

    // Build query for store deliveries using the COD codes
    const placeholders = storeCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_carton), 0) as sum_total_carton_store
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...storeCodes]

    // Add division filter if specified
    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_carton store query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalCartonStore = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_carton store result: ${sumTotalCartonStore}`)
      return sumTotalCartonStore
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_carton store:', error)
    throw error
  }
}

// Helper function to calculate sum of total_pcs for Store (N_A_OUT_STORE_SUM_PCS)
const calculateSumTotalPcsStore = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    // First, get COD codes from cod table with code_type 'Store'
    const codQuery = `
      SELECT code 
      FROM cod 
      WHERE code_type = 'Store'
    `

    console.log('Executing COD query:', codQuery)
    const codResult = await executeQuery(codQuery, [])

    if (!codResult?.result?.resultRows || codResult.result.resultRows.length === 0) {
      console.log('No Store codes found, returning 0')
      return 0
    }

    // Extract codes from result
    const storeCodes = codResult.result.resultRows.map((row: unknown[]) => row[0] as string)
    console.log('Found Store codes:', storeCodes)

    // Build query for store deliveries using the COD codes
    const placeholders = storeCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_pcs), 0) as sum_total_pcs_store
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...storeCodes]

    // Add division filter if specified
    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_pcs store query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalPcsStore = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_pcs store result: ${sumTotalPcsStore}`)
      return sumTotalPcsStore
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_pcs store:', error)
    throw error
  }
}

// Helper function to calculate sum of total_m3 for Store (N_A_OUT_STORE_SUM_M3)
const calculateSumTotalM3Store = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    // First, get COD codes from cod table with code_type 'Store'
    const codQuery = `
      SELECT code 
      FROM cod 
      WHERE code_type = 'Store'
    `

    console.log('Executing COD query:', codQuery)
    const codResult = await executeQuery(codQuery, [])

    if (!codResult?.result?.resultRows || codResult.result.resultRows.length === 0) {
      console.log('No Store codes found, returning 0')
      return 0
    }

    // Extract codes from result
    const storeCodes = codResult.result.resultRows.map((row: unknown[]) => row[0] as string)
    console.log('Found Store codes:', storeCodes)

    // Build query for store deliveries using the COD codes
    const placeholders = storeCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_m3), 0) as sum_total_m3_store
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...storeCodes]

    // Add division filter if specified
    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_m3 store query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalM3Store = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_m3 store result: ${sumTotalM3Store}`)
      return sumTotalM3Store
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_m3 store:', error)
    throw error
  }
}

// Helper function to calculate sum of total_carton for Customer (N_A_OUT_CUSTOMER_SUM_CARTON)
const calculateSumTotalCartonCustomer = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    // First, get COD codes from cod table with code_type 'Customer'
    const codQuery = `
      SELECT code 
      FROM cod 
      WHERE code_type = 'Customer'
    `

    console.log('Executing COD query:', codQuery)
    const codResult = await executeQuery(codQuery, [])

    if (!codResult?.result?.resultRows || codResult.result.resultRows.length === 0) {
      console.log('No Customer codes found, returning 0')
      return 0
    }

    // Extract codes from result
    const customerCodes = codResult.result.resultRows.map((row: unknown[]) => row[0] as string)
    console.log('Found Customer codes:', customerCodes)

    // Build query for customer deliveries using the COD codes
    const placeholders = customerCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_carton), 0) as sum_total_carton_customer
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...customerCodes]

    // Add division filter if specified
    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_carton customer query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalCartonCustomer = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_carton customer result: ${sumTotalCartonCustomer}`)
      return sumTotalCartonCustomer
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_carton customer:', error)
    throw error
  }
}

// Helper function to calculate sum of total_pcs for Customer (N_A_OUT_CUSTOMER_SUM_PCS)
const calculateSumTotalPcsCustomer = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    // First, get COD codes from cod table with code_type 'Customer'
    const codQuery = `
      SELECT code 
      FROM cod 
      WHERE code_type = 'Customer'
    `

    console.log('Executing COD query:', codQuery)
    const codResult = await executeQuery(codQuery, [])

    if (!codResult?.result?.resultRows || codResult.result.resultRows.length === 0) {
      console.log('No Customer codes found, returning 0')
      return 0
    }

    // Extract codes from result
    const customerCodes = codResult.result.resultRows.map((row: unknown[]) => row[0] as string)
    console.log('Found Customer codes:', customerCodes)

    // Build query for customer deliveries using the COD codes
    const placeholders = customerCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_pcs), 0) as sum_total_pcs_customer
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...customerCodes]

    // Add division filter if specified
    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_pcs customer query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalPcsCustomer = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_pcs customer result: ${sumTotalPcsCustomer}`)
      return sumTotalPcsCustomer
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_pcs customer:', error)
    throw error
  }
}

// Helper function to calculate sum of total_m3 for Customer (N_A_OUT_CUSTOMER_SUM_M3)
const calculateSumTotalM3Customer = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    // First, get COD codes from cod table with code_type 'Customer'
    const codQuery = `
      SELECT code 
      FROM cod 
      WHERE code_type = 'Customer'
    `

    console.log('Executing COD query:', codQuery)
    const codResult = await executeQuery(codQuery, [])

    if (!codResult?.result?.resultRows || codResult.result.resultRows.length === 0) {
      console.log('No Customer codes found, returning 0')
      return 0
    }

    // Extract codes from result
    const customerCodes = codResult.result.resultRows.map((row: unknown[]) => row[0] as string)
    console.log('Found Customer codes:', customerCodes)

    // Build query for customer deliveries using the COD codes
    const placeholders = customerCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_m3), 0) as sum_total_m3_customer
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...customerCodes]

    // Add division filter if specified
    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_m3 customer query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalM3Customer = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_m3 customer result: ${sumTotalM3Customer}`)
      return sumTotalM3Customer
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_m3 customer:', error)
    throw error
  }
}

// Helper function to save auto function result
const saveAutoFunctionResult = async (
  autoFunctionId: number,
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
  value: number,
): Promise<void> => {
  try {
    // Check if record already exists
    let checkQuery = `
      SELECT id FROM auto_function_customer_detail
      WHERE auto_function_id = ? AND customer_id = ? AND year_month = ?
    `

    const checkParams = [autoFunctionId, customerId, yearMonth]

    // Add division filter to check query
    if (divisionId) {
      checkQuery += ' AND division_id = ?'
      checkParams.push(divisionId)
    } else {
      checkQuery += ' AND division_id IS NULL'
    }

    const existingRecord = await executeQuery(checkQuery, checkParams)
    const existingRecords = existingRecord?.result?.resultRows || []

    if (existingRecords.length > 0) {
      // Update existing record
      let updateQuery = `
        UPDATE auto_function_customer_detail
        SET value = ?, updated_at = CURRENT_TIMESTAMP
        WHERE auto_function_id = ? AND customer_id = ? AND year_month = ?
      `

      const updateParams = [value, autoFunctionId, customerId, yearMonth]

      if (divisionId) {
        updateQuery += ' AND division_id = ?'
        updateParams.push(divisionId)
      } else {
        updateQuery += ' AND division_id IS NULL'
      }

      await executeQuery(updateQuery, updateParams)
      console.log('Updated existing auto function detail record')
    } else {
      // Insert new record
      const insertQuery = `
        INSERT INTO auto_function_customer_detail (
          auto_function_id, customer_id, division_id, year_month, value, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `

      const insertParams = [autoFunctionId, customerId, divisionId, yearMonth, value]

      await executeQuery(insertQuery, insertParams)
      console.log('Inserted new auto function detail record')
    }
  } catch (error) {
    console.error('Error saving auto function result:', error)
    throw error
  }
}

//calculate AutoFunction
const caculateAutoFunction = async (): Promise<boolean> => {
  try {
    console.log('Starting auto function calculation...')

    // Check if we have selected customer and division
    if (!selectedCustomerId.value) {
      console.warn('No customer selected for auto function calculation')
      return false
    }

    // Get the current year-month from selectedMonth
    if (!selectedMonth.value) {
      console.warn('No month selected for auto function calculation')
      return false
    }

    const yearMonth = selectedMonth.value // Format should be 'YYYY-MM'

    // Get auto functions for the selected customer
    const autoFunctionsQuery = `
      SELECT af.id, af.code, af.name
      FROM auto_function af
      INNER JOIN auto_function_customers afc ON af.id = afc.auto_function_id
      WHERE afc.customer_id = ?
    `

    const autoFunctionsResult = await executeQuery(autoFunctionsQuery, [selectedCustomerId.value])

    const autoFunctions =
      autoFunctionsResult?.result?.resultRows?.map((row: unknown[]) => ({
        id: Number(row[0]),
        code: String(row[1]),
        name: String(row[2]),
      })) || []

    if (autoFunctions.length === 0) {
      console.log('No auto functions found for selected customer')
      return true // Not an error if no auto functions are configured
    }

    console.log('Found auto functions:', autoFunctions)

    // Process each auto function
    for (const autoFunction of autoFunctions) {
      const { id: autoFunctionId, code: functionCode, name: functionName } = autoFunction

      console.log(`Processing auto function: ${functionCode} (${functionName})`)

      let calculatedValue = 0

      // Process based on auto function code
      switch (functionCode) {
        case 'N_A_OUT_SUM_CARTON':
          calculatedValue = await calculateSumTotalCarton(
            selectedCustomerId.value,
            selectedDivisionId.value,
            yearMonth,
          )
          break

        case 'N_A_OUT_SUM_PCS':
          calculatedValue = await calculateSumTotalPcs(
            selectedCustomerId.value,
            selectedDivisionId.value,
            yearMonth,
          )
          break

        case 'N_A_OUT_SUM_M3':
          calculatedValue = await calculateSumTotalM3(
            selectedCustomerId.value,
            selectedDivisionId.value,
            yearMonth,
          )
          break

        case 'N_A_OUT_STORE_SUM_CARTON':
          calculatedValue = await calculateSumTotalCartonStore(
            selectedCustomerId.value,
            selectedDivisionId.value,
            yearMonth,
          )
          break

        case 'N_A_OUT_STORE_SUM_PCS':
          calculatedValue = await calculateSumTotalPcsStore(
            selectedCustomerId.value,
            selectedDivisionId.value,
            yearMonth,
          )
          break

        case 'N_A_OUT_STORE_SUM_M3':
          calculatedValue = await calculateSumTotalM3Store(
            selectedCustomerId.value,
            selectedDivisionId.value,
            yearMonth,
          )
          break

        case 'N_A_OUT_CUSTOMER_SUM_CARTON':
          calculatedValue = await calculateSumTotalCartonCustomer(
            selectedCustomerId.value,
            selectedDivisionId.value,
            yearMonth,
          )
          break

        case 'N_A_OUT_CUSTOMER_SUM_PCS':
          calculatedValue = await calculateSumTotalPcsCustomer(
            selectedCustomerId.value,
            selectedDivisionId.value,
            yearMonth,
          )
          break

        case 'N_A_OUT_CUSTOMER_SUM_M3':
          calculatedValue = await calculateSumTotalM3Customer(
            selectedCustomerId.value,
            selectedDivisionId.value,
            yearMonth,
          )
          break

        // Add more cases here as needed for other auto function codes
        default:
          console.log(`Auto function code '${functionCode}' not implemented yet`)
          continue
      }

      // Save the calculated value to auto_function_customer_detail
      await saveAutoFunctionResult(
        autoFunctionId,
        selectedCustomerId.value,
        selectedDivisionId.value,
        yearMonth,
        calculatedValue,
      )

      console.log(`Saved auto function result: ${functionCode} = ${calculatedValue}`)
    }

    console.log('Auto function calculation completed successfully')
    return true
  } catch (error) {
    console.error('Error in auto function calculation:', error)
    return false
  }
}

// Cancel import
const cancelImport = () => {
  showConfirmDialog.value = false
}

// Reset form function
const resetForm = () => {
  // Reset all form state
  fileSelected.value = null
  uploadSuccess.value = false
  showSuccess.value = false
  showError.value = false
  successMessage.value = ''
  errorMessage.value = ''
  importedData.value = []
  columns.value = []
  pagination.current = 1
  pagination.total = 0
  processingProgress.value = 0
  showProgressBar.value = false
  isLoading.value = false
  isImporting.value = false
  columnMapping.value = {} // Clear column mappings
  selectedExcelColumn.value = null // Clear selected column
}

// Handle table pagination change
const handleTableChange = (pag: { current: number; pageSize: number }) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

// Download sample file function
const downloadSampleFile = () => {
  const link = document.createElement('a')
  link.href = '/ImportSample/OutBound.xlsx'
  link.download = 'outbound.xlsx'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// Function to handle actual import after confirmation
const confirmImport = async () => {
  showConfirmDialog.value = false
  isImporting.value = true

  try {
    // Perform additional data validation before import
    const validation = validateExcelData(importedData.value)

    if (!validation.isValid) {
      throw new Error(validation.errorMessage)
    }

    // Check for duplicate data in the imported file
    const duplicateCheck = checkForDuplicatesOutbound(importedData.value)
    if (duplicateCheck.hasDuplicates) {
      throw new Error(duplicateCheck.duplicateMessage)
    }

    // Initialize database if not already done
    await initialize()

    // Prepare data for import with customer and month information
    const selectedCustomer = customers.value.find((c) => c.id === selectedCustomerId.value)

    // Handle month conversion safely
    const monthYear = selectedMonth.value // Use the string format directly (YYYY-MM)

    console.log('Parsed monthYear:', monthYear)

    console.log('Importing data with:', {
      customer: selectedCustomer,
      division: selectedDivisionId.value,
      month: monthYear,
      rowCount: importedData.value.length,
    })

    // Create mapping from Excel columns to DB columns
    const excelToDbMapping = columnMapping.value
    const mappedDbColumns = Object.values(excelToDbMapping)

    // Build the INSERT SQL dynamically based on mapped columns
    const baseColumns = ['customer_id', 'division_id']
    const allColumns = [...baseColumns, ...mappedDbColumns]
    const placeholders = allColumns.map(() => '?').join(', ')

    const insertSQL = `
      INSERT INTO customer_outbound (${allColumns.join(', ')})
      VALUES (${placeholders})
    `

    console.log('Insert SQL:', insertSQL)

    let successCount = 0
    let errorCount = 0
    const errors: string[] = []

    for (let i = 0; i < importedData.value.length; i++) {
      try {
        const row = importedData.value[i]
        const values: (string | number | boolean | null)[] = []

        // Add customer_id first
        values.push(selectedCustomerId.value)

        // Add division_id (optional - can be null)
        values.push(selectedDivisionId.value)

        // Add mapped column values
        for (const dbColumn of mappedDbColumns) {
          // Find the excel column that maps to this db column
          const excelColumn = Object.keys(excelToDbMapping).find(
            (excelCol) => excelToDbMapping[excelCol] === dbColumn,
          )

          if (excelColumn && row[excelColumn] !== undefined) {
            let value = row[excelColumn]

            // Handle date formatting for date columns
            if (dbColumn === 'sph_ship_ymd' && typeof value === 'string') {
              // Try to parse and format the date
              value = formatDate(value)
            }

            // Handle numeric conversions
            if (typeof value === 'string' && value.trim() === '') {
              value = null // Convert empty strings to null
            } else if (typeof value === 'number' || !isNaN(Number(value))) {
              // Keep numeric values as numbers if they're numeric columns
              const numericColumns = ['spr_rtpc_qty']
              if (numericColumns.includes(dbColumn)) {
                value = Number(value) || 0
              }
            } else if (typeof value === 'boolean') {
              // Convert boolean to string or number as needed
              value = value ? 1 : 0
            }

            values.push(value)
          } else {
            // Handle unmapped or missing columns
            values.push(null)
          }
        }

        console.log(`Row ${i + 1} values:`, values)

        // Execute the INSERT
        await executeQuery(insertSQL, values)
        successCount++
      } catch (error) {
        console.error(`Error inserting row ${i + 1}:`, error)
        errorCount++
        errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

    if (errorCount > 0) {
      console.warn(`Import completed with ${errorCount} errors:`, errors)
      if (successCount === 0) {
        throw new Error(
          `Failed to import any records. Errors: ${errors.slice(0, 3).join('; ')}${errors.length > 3 ? '...' : ''}`,
        )
      } else {
        // Show warning but continue with success message
        console.warn(`Partial import: ${successCount} successful, ${errorCount} failed`)
      }
    }

    //calculate Auto Function
    const autoFunctionResult = await caculateAutoFunction()

    if (!autoFunctionResult) {
      errorMessage.value =
        'Error: Failed to calculate auto functions after successful import. Please check the data and try again.'
      showError.value = true
      return
    }

    console.log('Import completed:')

    // Show success message using our function
    showSuccessMessage(
      `${successCount} records imported successfully for ${selectedCustomer?.name}!${errorCount > 0 ? ` (${errorCount} errors)` : ''}`,
    )

    // Optionally reset the form after successful import
    // clearFile();
  } catch (error) {
    console.error('Error importing data:', error)
    errorMessage.value = `Error importing data: ${error instanceof Error ? error.message : String(error)}`
    showError.value = true
  } finally {
    isImporting.value = false
  }
}

// Helper function to format dates
const formatDate = (dateStr: string): string => {
  try {
    // Try to parse the date string and format it consistently
    const date = new Date(dateStr)
    if (!isNaN(date.getTime())) {
      // Format as YYYY-MM-DD
      return date.toISOString().split('T')[0]
    }
    return dateStr
  } catch {
    return dateStr
  }
}

// Function to check for duplicates in outbound data
const checkForDuplicatesOutbound = (
  data: ExcelRowData[],
): { hasDuplicates: boolean; duplicateMessage?: string } => {
  const duplicateKeys = new Set<string>()
  const seenCombinations = new Set<string>()

  // Get the Excel columns that map to our duplicate check columns
  const excelToDbMapping = columnMapping.value
  const sprAsNumColumn = Object.keys(excelToDbMapping).find(
    (col) => excelToDbMapping[col] === 'spr_as_num',
  )
  const sprAslnNumColumn = Object.keys(excelToDbMapping).find(
    (col) => excelToDbMapping[col] === 'spr_asln_num',
  )
  const sprAssqNumColumn = Object.keys(excelToDbMapping).find(
    (col) => excelToDbMapping[col] === 'spr_assq_num',
  )

  // If not all required columns are mapped, we can't check for duplicates
  if (!sprAsNumColumn || !sprAslnNumColumn || !sprAssqNumColumn) {
    return { hasDuplicates: false }
  }

  for (let i = 0; i < data.length; i++) {
    const row = data[i]
    const sprAsNum = row[sprAsNumColumn]?.toString().trim() || ''
    const sprAslnNum = row[sprAslnNumColumn]?.toString().trim() || ''
    const sprAssqNum = row[sprAssqNumColumn]?.toString().trim() || ''

    // Create combination key for duplicate checking
    const combinationKey = `${sprAsNum}|${sprAslnNum}|${sprAssqNum}`

    if (seenCombinations.has(combinationKey)) {
      duplicateKeys.add(`Row ${i + 1}: ${sprAsNum}, ${sprAslnNum}, ${sprAssqNum}`)
    } else {
      seenCombinations.add(combinationKey)
    }
  }

  if (duplicateKeys.size > 0) {
    const duplicateList = Array.from(duplicateKeys).slice(0, 5).join('\n') // Show first 5 duplicates
    const moreText =
      duplicateKeys.size > 5 ? `\n... and ${duplicateKeys.size - 5} more duplicates` : ''
    return {
      hasDuplicates: true,
      duplicateMessage: `Duplicate records found in the file based on (spr_as_num, spr_asln_num, spr_assq_num):\n${duplicateList}${moreText}`,
    }
  }

  return { hasDuplicates: false }
}

// Initialize the component
const initializeComponent = async () => {
  await loadCustomers()
}

// Call initialization when component is ready
initializeComponent()
</script>

<template>
  <div class="import-container">
    <div class="page-header">
      <h2>Import Outbound</h2>
      <p>Upload and import outbound records from Excel files</p>
      <p>
        Download sample file
        <a
          @click="downloadSampleFile"
          style="color: #1890ff; cursor: pointer; text-decoration: underline"
          >outbound.xlsx</a
        >
      </p>
    </div>
    <!-- Search Filters -->
    <div class="search-section">
      <a-card class="search-card">
        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="Customer">
                <a-select
                  v-model:value="selectedCustomerId"
                  placeholder="Select customer"
                  allow-clear
                  show-search
                  :filter-option="filterCustomerOption"
                  @change="handleCustomerChange"
                >
                  <a-select-option
                    v-for="customer in customers"
                    :key="customer.id"
                    :value="customer.id"
                  >
                    {{ customer.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="Division">
                <a-select
                  v-model:value="selectedDivisionId"
                  placeholder="Select division (optional)"
                  allow-clear
                  show-search
                  :disabled="!selectedCustomerId"
                  :filter-option="filterDivisionOption"
                >
                  <a-select-option
                    v-for="division in divisions"
                    :key="division.id"
                    :value="division.id"
                  >
                    {{ division.name }} ({{ division.code }})
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="Month">
                <a-date-picker
                  v-model:value="selectedMonth"
                  picker="month"
                  placeholder="Select month"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  @change="handleMonthChange"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8"> </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- Error message -->
    <a-alert
      v-if="showError"
      type="error"
      style="margin-bottom: 10px"
      :message="errorMessage"
      show-icon
    />

    <!-- Success message -->
    <a-alert
      v-if="showSuccess"
      style="margin-bottom: 10px"
      type="success"
      :message="successMessage"
      show-icon
    />

    <!-- Progress bar for large file processing -->
    <div v-if="showProgressBar && isLoading" class="progress-container">
      <div class="progress-label">Processing large file: {{ processingProgress }}%</div>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: processingProgress + '%' }"></div>
      </div>
    </div>

    <!-- File upload area -->
    <div class="file-upload">
      <!-- File metadata if a file is selected -->
      <div v-if="fileSelected" class="file-metadata">
        <div class="metadata-item">
          <span class="metadata-label">File name:</span>
          <span class="metadata-value">{{ fileSelected.name }}</span>
        </div>
        <div class="metadata-item">
          <span class="metadata-label">Total Records:</span>
          <span class="metadata-value">{{ importedData.length }}</span>
        </div>
        <div class="metadata-item">
          <span class="metadata-label">Columns:</span>
          <span class="metadata-value">{{ columns.length }}</span>
        </div>
      </div>

      <!-- Drop zone - hidden after successful upload -->
      <!-- File input -->
      <input
        ref="fileInputRef"
        type="file"
        accept=".xlsx,.xls,.csv"
        @change="handleFileSelect"
        class="hidden-input"
      />

      <div
        v-if="!uploadSuccess"
        class="drop-zone"
        :class="{ active: isDragging }"
        @dragenter="handleDragEnter"
        @dragover="handleDragOver"
        @dragleave="handleDragLeave"
        @drop="handleDrop"
        @click="() => fileInputRef?.click()"
      >
        <div class="drop-message">
          <div class="upload-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="17 8 12 3 7 8"></polyline>
              <line x1="12" y1="3" x2="12" y2="15"></line>
            </svg>
          </div>
          <div class="drop-text">
            <span class="primary-text">Drag and drop Excel files here</span>
            <span class="secondary-text">or click here to select files</span>
          </div>
        </div>
      </div>

      <!-- Form buttons -->
      <div class="form-actions">
        <a-button
          type="default"
          :disabled="!fileSelected || isLoading"
          @click="clearFile"
          :loading="isLoading"
        >
          {{ isLoading ? 'Processing...' : 'Clear' }}
        </a-button>

        <a-button
          type="primary"
          :disabled="importedData.length === 0 || isImporting"
          @click="importData"
          :loading="isImporting"
        >
          {{ isImporting ? 'Importing...' : 'Import Data' }}
        </a-button>
      </div>
    </div>

    <!-- Imported data table -->
    <div v-if="importedData.length > 0" class="data-table-container">
      <h3>Excel Data</h3>

      <!-- Column mapping interface -->
      <div v-if="showMapping && uploadSuccess" class="mapping-container">
        <a-row :gutter="16">
          <a-col :span="12">
            <h3>Column Mapping</h3>
          </a-col>
          <a-col :span="12" style="text-align: right">
            <a-space>
              <a-button
                @click="saveMappingWithConfirm"
                type="primary"
                :disabled="Object.keys(columnMapping).length === 0"
              >
                Save Mapping
              </a-button>
              <a-button @click="clearAllMappings" type="default"> Clear All Mappings </a-button>
            </a-space>
          </a-col>
        </a-row>

        <p class="mapping-instructions">Map your Excel columns to database fields</p>
        <div class="mapping-interface">
          <div class="excel-columns">
            <h4>Excel Columns</h4>
            <div class="column-list">
              <div
                v-for="column in columns"
                :key="column.dataIndex"
                class="column-item excel-column"
                :class="{
                  mapped: columnMapping[column.title],
                  active: selectedExcelColumn === column.title,
                }"
                @click="selectExcelColumn(column.title)"
              >
                <span class="column-name">{{ column.title }}</span>
                <span v-if="columnMapping[column.title]" class="mapping-arrow">→</span>
                <span v-if="columnMapping[column.title]" class="mapped-to">
                  {{ columnMapping[column.title] }}
                  <a-button
                    @click.stop="clearMapping(column.title)"
                    type="link"
                    size="small"
                    class="clear-mapping-btn"
                  >
                    ×
                  </a-button>
                </span>
              </div>
            </div>
          </div>

          <div class="db-columns">
            <h4>Database Columns</h4>
            <div class="column-list">
              <div
                v-for="column in databaseColumns"
                :key="column"
                class="column-item db-column"
                :class="{
                  mapped: Object.values(columnMapping).includes(column),
                  disabled: Object.values(columnMapping).includes(column),
                }"
                @click="updateColumnMapping(column)"
              >
                <span class="column-name">{{ column }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <a-table
        :columns="columns"
        :data-source="importedData"
        :pagination="pagination"
        :loading="isLoading"
        @change="handleTableChange"
        :scroll="{ x: '100%' }"
        class="import-table"
      />
    </div>
  </div>

  <!-- Confirmation dialog -->
  <a-modal
    v-if="showConfirmDialog"
    :visible="showConfirmDialog"
    title="Confirm Import"
    @cancel="cancelImport"
    :maskClosable="false"
    :width="600"
  >
    <div class="confirm-import-content">
      <p>Are you sure you want to import {{ importedData.length }} records?</p>
      <div class="mapping-summary">
        <h4>Column Mapping Summary</h4>
        <div class="mapping-table">
          <table>
            <thead>
              <tr>
                <th>Excel Column</th>
                <th>Database Column</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(dbColumn, excelColumn) in columnMapping" :key="excelColumn">
                <td>{{ excelColumn }}</td>
                <td>{{ dbColumn }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button key="back" @click="cancelImport"> Cancel </a-button>
      <a-button key="submit" type="primary" :loading="isImporting" @click="confirmImport">
        Import
      </a-button>
    </template>
  </a-modal>

  <!-- Save mapping dialog -->
  <a-modal
    v-model:visible="showSaveMappingDialog"
    title="Save Column Mapping"
    @ok="saveMapping"
    @cancel="showSaveMappingDialog = false"
    :maskClosable="false"
    :width="600"
  >
    <p>Are you sure you want to save the current column mapping for this customer?</p>
    <p>This will replace any existing mapping configuration.</p>
    <div v-if="Object.keys(columnMapping).length > 0" style="margin-top: 16px">
      <h4>Current Mappings:</h4>
      <div style="background: #f5f5f5; padding: 12px; border-radius: 4px">
        <div
          v-for="(dbColumn, excelColumn) in columnMapping"
          :key="excelColumn"
          style="margin-bottom: 4px"
        >
          <strong>{{ excelColumn }}</strong> → {{ dbColumn }}
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped>
.import-container {
  padding: 24px;
  background: #fff;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #262626;
}

.page-header p {
  color: #8c8c8c;
  margin: 0;
  font-size: 14px;
}

.search-section {
  margin-bottom: 24px;
}

.search-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

@media (max-width: 768px) {
  .import-container {
    padding: 16px;
  }

  .page-header h2 {
    font-size: 20px;
  }
}

.instructions {
  margin-bottom: 15px;
  color: #666;
}

.file-upload {
  margin-bottom: 20px;
}

.file-input-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  justify-content: flex-start;
}

.hidden-input {
  display: none;
}

.file-metadata {
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
  font-size: 14px;
}

.metadata-item {
  margin-bottom: 5px;
  display: flex;
}

.clear-button {
  margin-left: 10px;
}

.metadata-label {
  font-weight: bold;
  width: 120px;
  color: #666;
}

.metadata-value {
  flex: 1;
}

.drop-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.drop-zone.active {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.05);
}

.drop-message {
  color: #777;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  margin-bottom: 15px;
  color: #999;
}

.drop-text {
  display: flex;
  flex-direction: column;
}

.primary-text {
  font-size: 16px;
  margin-bottom: 5px;
  font-weight: 500;
}

.secondary-text {
  font-size: 14px;
  color: #999;
}

.form-actions {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}

/* Table styling */
.data-table-container {
  margin-top: 30px;
  width: 100%;
  overflow-x: auto;
}

/* Data summary styling */
.data-summary {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.summary-card {
  background-color: v-bind('Colors.colorSuccessBg');
  border: 1px solid v-bind('Colors.colorSuccessBorder');
  border-radius: 8px;
  padding: 15px;
  min-width: 150px;
  flex: 1;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive layout */
@media (max-width: 768px) {
  .data-summary {
    flex-direction: column;
  }

  .summary-card {
    width: 100%;
    margin-bottom: 10px;
  }
}

.summary-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  text-transform: uppercase;
  font-weight: bold;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

/* Ant Design Table styles override */
:deep(.ant-table-wrapper) {
  margin-bottom: 20px;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #f8f8f8;
  font-weight: bold;
  color: #333;
  white-space: nowrap;
}

:deep(.ant-table-tbody > tr > td) {
  white-space: nowrap;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

:deep(.ant-pagination) {
  text-align: center;
  margin-top: 20px;
}

/* Table horizontal scroll styles */
.import-table {
  width: 100%;
  overflow-x: auto;
}

/* Specific column styling */
:deep(.date-column) {
  background-color: #f0f7ff;
}

/* Loading indicator styling */
:deep(.ant-spin) {
  margin-top: 20px;
}

/* Better scrollbar styling for table */
:deep(.ant-table-body)::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

:deep(.ant-table-body)::-webkit-scrollbar-track {
  background: #f1f1f1;
}

:deep(.ant-table-body)::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 4px;
}

:deep(.ant-table-body)::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

/* Progress bar styling */
.progress-container {
  margin: 15px 0;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.progress-label {
  margin-bottom: 5px;
  font-size: 14px;
  color: #666;
}

.progress-bar {
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #1890ff;
  border-radius: 4px;
  transition: width 0.3s ease;
  background-size: 40px 40px;
}

/* Column mapping styling */
.mapping-container {
  margin-bottom: 30px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.mapping-instructions {
  margin-bottom: 15px;
  color: #666;
  font-size: 14px;
}

.mapping-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-bottom: 15px;
}

.mapping-interface {
  display: flex;
  gap: 20px;
  justify-content: space-between;
}

.excel-columns,
.db-columns {
  flex: 1;
  max-width: 45%;
}

.excel-columns h4,
.db-columns h4 {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #e0e0e0;
  color: #333;
}

.column-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.column-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.excel-column:hover {
  background-color: #f0f7ff;
}

.db-column:hover:not(.disabled) {
  background-color: #f0fff0;
}

.column-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.column-item.mapped {
  background-color: #f6ffed;
}

.column-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.column-name {
  flex: 1;
}

.mapping-arrow {
  margin: 0 8px;
  color: #1890ff;
  font-weight: bold;
}

.mapped-to {
  background-color: #f6ffed;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #b7eb8f;
  display: flex;
  align-items: center;
}

.clear-mapping-btn {
  padding: 0 0 0 4px;
  height: auto;
  line-height: 1;
  color: #ff4d4f;
}

.clear-mapping-btn:hover {
  color: #cf1322;
}

.selected-customer {
  margin-top: 10px;
}

.customer-detail-card {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 15px;
}

.customer-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.customer-id {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.customer-address,
.customer-contact {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

/* Confirmation dialog styles */
.confirm-import-content {
  max-height: 400px;
  overflow-y: auto;
}

.import-summary {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.summary-item {
  display: flex;
  margin-bottom: 8px;
}

.summary-label {
  font-weight: bold;
  width: 100px;
}

.summary-value {
  flex: 1;
}

.mapping-summary {
  margin-top: 20px;
}

.mapping-summary h4 {
  margin-bottom: 10px;
  color: #333;
}

.mapping-table {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.mapping-table table {
  width: 100%;
  border-collapse: collapse;
}

.mapping-table th {
  background-color: #fafafa;
  padding: 12px;
  text-align: left;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
}

.mapping-table td {
  padding: 10px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.mapping-table tr:last-child td {
  border-bottom: none;
}

.mapping-table tr:hover td {
  background-color: #f5f5f5;
}
</style>
