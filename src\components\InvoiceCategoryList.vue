<template>
  <div class="invoice-category-list">
    <div v-for="category in categories" :key="category.id" class="category-section">
      <div class="category-header">
        <div class="category-title">
          <span class="category-name">{{ category.name }}</span>
          <a-button
            v-if="!readonly"
            type="link"
            danger
            @click="handleDeleteCategory(category.id)"
            class="delete-category-btn"
          >
            <template #icon><DeleteOutlined /></template>
          </a-button>
        </div>
        <a-button
          v-if="!readonly"
          type="primary"
          ghost
          @click="handleAddCharge(category.id)"
          class="add-charge-btn"
        >
          <template #icon><PlusOutlined /></template>
          Add Charge
        </a-button>
      </div>

      <a-table
        :columns="getColumns"
        :data-source="category.charges"
        row-key="id"
        size="middle"
        :pagination="false"
        :scroll="{ x: 1000 }"
        class="charge-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'calculationType'">
            <a-tag :color="getCalculationTypeColor(record.workoutId)">
              {{ getCalculationType(record.workoutId) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'workout'">
            <template v-if="readonly">
              <span>{{ getWorkoutName(record.workoutId) }}</span>
              <a-tooltip
                v-if="isLinkedWorkout(record.workoutId)"
                :title="getLinkedWorkoutTooltip(record.workoutId)"
              >
                <LinkOutlined style="margin-left: 8px; color: #1890ff" />
              </a-tooltip>
            </template>
            <template v-else>
              <a-select
                v-model:value="record.workoutId"
                style="width: 100%"
                @change="(value: number) => handleWorkoutChange(category.id, record, value)"
              >
                <a-select-option
                  v-for="w in categoryWorkouts(category.categoryId)"
                  :key="w.id"
                  :value="w.id"
                >
                  {{ w.name }}
                </a-select-option>
              </a-select>
            </template>
          </template>
          <template v-else-if="column.key === 'unit'">
            <span>{{ getUnitName(record.unitId) }}</span>
          </template>
          <template v-else-if="column.key === 'unitPrice'">
            <span>{{ formatCurrency(record.unitPrice) }}</span>
          </template>
          <template v-else-if="column.key === 'quantity'">
            <template v-if="readonly">
              <span>{{ record.quantity }}</span>
            </template>
            <template v-else>
              <a-input-number
                v-model:value="record.quantity"
                style="width: 100%"
                :precision="2"
                :disabled="isAutoCalculated(record.workoutId)"
                @change="(value: number) => handleQuantityChange(category.id, record, value)"
              />
            </template>
          </template>
          <template v-else-if="column.key === 'total'">
            {{ formatCurrency(record.totalAmount) }}
          </template>
          <template v-else-if="column.key === 'vatRate'">
            <span>{{ getVatRate(record.workoutId) }}%</span>
          </template>
          <template v-else-if="column.key === 'vatAmount'">
            <span>{{ formatCurrency(calculateVatAmount(record)) }}</span>
          </template>
          <template v-else-if="column.key === 'debitCode'">
            <span>{{ getDebitCodeName(record.debitCodeId) }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-button
              v-if="!readonly"
              type="link"
              danger
              size="small"
              @click="$emit('delete-charge', category.id, record.id)"
            >
              <template #icon><DeleteOutlined /></template>
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { PlusOutlined, DeleteOutlined, LinkOutlined } from '@ant-design/icons-vue'
import type { InvoiceCategory, InvoiceCharge } from '../types/invoice'
import type { Category } from '../types/MasterDataTypes/Category'
import type { Unit } from '../types/MasterDataTypes/Unit'
import type { Workout } from '../types/MasterDataTypes/Category'
import type { DebitCode } from '../types/CompanyProfile'
import { useInvoiceService } from '../services/invoiceLocalService'
import { getDebitCodes } from '../services/master-data/companyProfile/companyProfileService'

const props = defineProps<{
  categories: InvoiceCategory[]
  allCategories: Category[]
  workouts: Workout[]
  units: Unit[]
  customerId?: number
  customerDivisionId?: number
  readonly?: boolean
}>()

const emit = defineEmits<{
  (e: 'add-charge', categoryId: string): void
  (e: 'update-charge', categoryId: string, charge: InvoiceCharge): void
  (e: 'delete-charge', categoryId: string, chargeId: string): void
  (e: 'delete-category', categoryId: string): void
}>()

const invoiceService = useInvoiceService()
const debitCodes = ref<DebitCode[]>([])

onMounted(async () => {
  try {
    debitCodes.value = await getDebitCodes()
  } catch (error) {
    console.error('Error loading debit codes:', error)
  }
})

// Add calculation type functions
const getCalculationType = (workoutId: number | undefined) => {
  if (!workoutId) return ''
  const workout = props.workouts.find((w) => w.id === workoutId)
  return workout?.calculationType || ''
}

const getCalculationTypeColor = (workoutId: number | undefined) => {
  if (!workoutId) return ''
  const workout = props.workouts.find((w) => w.id === workoutId)
  switch (workout?.calculationType) {
    case 'Manual':
      return 'blue'
    case 'Link':
      return 'green'
    case 'Auto':
      return 'orange'
    default:
      return ''
  }
}

interface TableColumn {
  title: string
  key: string
  dataIndex: string
  width: number
  align?: 'left' | 'right' | 'center'
  ellipsis?: boolean
  fixed?: 'left' | 'right' | boolean
}

const getColumns = computed(() => {
  const baseColumns: TableColumn[] = [
    {
      title: 'Type',
      key: 'calculationType',
      dataIndex: 'workoutId',
      width: 100,
      ellipsis: true,
    },
    {
      title: 'Workout',
      key: 'workout',
      dataIndex: 'workoutId',
      width: 200,
      ellipsis: true,
    },
    {
      title: 'Unit',
      key: 'unit',
      dataIndex: 'unitId',
      width: 100,
      ellipsis: true,
    },
    {
      title: 'Unit Price',
      key: 'unitPrice',
      dataIndex: 'unitPrice',
      width: 120,
      align: 'right',
      ellipsis: true,
    },
    {
      title: 'Quantity',
      key: 'quantity',
      dataIndex: 'quantity',
      width: 100,
      align: 'right',
      ellipsis: true,
    },
    {
      title: 'VAT Rate',
      key: 'vatRate',
      dataIndex: 'vatRate',
      width: 100,
      align: 'right',
      ellipsis: true,
    },
    {
      title: 'VAT Amount',
      key: 'vatAmount',
      dataIndex: 'vatAmount',
      width: 120,
      align: 'right',
      ellipsis: true,
    },
    {
      title: 'Total',
      key: 'total',
      dataIndex: 'totalAmount',
      width: 120,
      align: 'right',
      ellipsis: true,
    },
    {
      title: 'Debit Code',
      key: 'debitCode',
      dataIndex: 'debitCodeId',
      width: 150,
      ellipsis: true,
    },
  ]

  // Only add action column if not readonly
  if (!props.readonly) {
    baseColumns.push({
      title: '',
      key: 'action',
      dataIndex: 'action',
      width: 60,
      align: 'center',
      fixed: 'right',
    })
  }

  return baseColumns
})

const categoryWorkouts = (categoryId: number) => {
  return props.workouts.filter((w) => w.categoryId === categoryId)
}

const getUnitName = (unitId: number | undefined) => {
  if (!unitId) return ''
  return props.units.find((u) => u.id === unitId)?.name || ''
}

const formatCurrency = (amount: number | undefined) => {
  if (amount === undefined) return '$0.00'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

const handleAddCharge = (categoryId: string) => {
  emit('add-charge', categoryId)
}

// Add update tracking to prevent infinite loops
const updatingCharges = ref(new Set<string>())

const getChargeKey = (categoryId: string, chargeId: string) => `${categoryId}-${chargeId}`

const calculateLinkedQuantity = (
  sourceWorkoutId: number,
  sourceQuantity: number,
  targetWorkoutId: number,
) => {
  const sourceWorkout = props.workouts.find((w) => w.id === sourceWorkoutId)
  const targetWorkout = props.workouts.find((w) => w.id === targetWorkoutId)

  if (!sourceWorkout || !targetWorkout) return sourceQuantity

  // For now, we use direct quantity. Can be extended with more complex calculations
  return sourceQuantity
}

const findLinkedCharge = (workoutId: number): InvoiceCharge | undefined => {
  for (const category of props.categories) {
    const charge = category.charges.find((c) => c.workoutId === workoutId)
    if (charge) return charge
  }
  return undefined
}

const findLinkedCharges = (workoutId: number): { categoryId: string; charge: InvoiceCharge }[] => {
  const result: { categoryId: string; charge: InvoiceCharge }[] = []
  props.categories.forEach((category) => {
    category.charges.forEach((charge) => {
      const workout = props.workouts.find((w) => w.id === charge.workoutId)
      if (workout?.calculationType === 'Link' && workout.linkedWorkoutId === workoutId) {
        result.push({ categoryId: category.id, charge })
      }
    })
  })
  return result
}

const updateLinkedWorkouts = (sourceWorkoutId: number, sourceQuantity: number) => {
  const linkedCharges = findLinkedCharges(sourceWorkoutId)
  linkedCharges.forEach(({ categoryId, charge }) => {
    const chargeKey = getChargeKey(categoryId, charge.id)
    if (!updatingCharges.value.has(chargeKey)) {
      updatingCharges.value.add(chargeKey)
      charge.quantity = calculateLinkedQuantity(
        sourceWorkoutId,
        sourceQuantity,
        charge.workoutId || 0,
      )
      calculateTotal(charge)
      emit('update-charge', categoryId, charge)
      updatingCharges.value.delete(chargeKey)
    }
  })
}

const handleWorkoutChange = async (
  categoryId: string,
  charge: InvoiceCharge,
  workoutId: number,
) => {
  const workout = props.workouts.find((w) => w.id === workoutId)
  if (workout) {
    charge.workoutId = workoutId
    charge.unitId = workout.unitId || undefined
    charge.unitPrice = workout.unitPrice || 0
    charge.debitCodeId = workout.debitCodeId || undefined
    charge.vatRate = workout.vat || 0

    // Handle linked workout calculation
    if (workout.calculationType === 'Link' && workout.linkedWorkoutId) {
      const linkedCharge = findLinkedCharge(workout.linkedWorkoutId)
      if (linkedCharge) {
        const chargeKey = getChargeKey(categoryId, charge.id)
        if (!updatingCharges.value.has(chargeKey)) {
          updatingCharges.value.add(chargeKey)
          charge.quantity = calculateLinkedQuantity(
            workout.linkedWorkoutId,
            linkedCharge.quantity || 0,
            workoutId,
          )
          updatingCharges.value.delete(chargeKey)
        }
      }
    }
    // Handle auto calculation if needed
    else if (workout.calculationType === 'Auto' && workout.autoFocusCode && props.customerId) {
      try {
        const currentDate = new Date()
        const yearMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`

        const autoValue = await invoiceService.getAutoFunctionValue({
          autoFocusCode: workout.autoFocusCode,
          customerId: props.customerId,
          divisionId: props.customerDivisionId,
          yearMonth,
        })

        if (autoValue) {
          charge.quantity = autoValue.value
        }
      } catch (error) {
        console.error('Error fetching auto function value:', error)
      }
    }

    calculateTotal(charge)
    emit('update-charge', categoryId, charge)
  }
}

const handleQuantityChange = (categoryId: string, charge: InvoiceCharge, quantity: number) => {
  const chargeKey = getChargeKey(categoryId, charge.id)
  if (!updatingCharges.value.has(chargeKey)) {
    updatingCharges.value.add(chargeKey)
    charge.quantity = quantity
    calculateTotal(charge)
    // Update any linked workouts that depend on this one
    if (charge.workoutId) {
      updateLinkedWorkouts(charge.workoutId, quantity)
    }
    emit('update-charge', categoryId, charge)
    updatingCharges.value.delete(chargeKey)
  }
}

const calculateTotal = (charge: InvoiceCharge) => {
  const baseAmount = (charge.unitPrice || 0) * (charge.quantity || 0)
  charge.totalAmount = baseAmount
  charge.vatAmount = calculateVatAmount(charge)
}

const handleDeleteCategory = (categoryId: string) => {
  emit('delete-category', categoryId)
}

const getWorkoutName = (workoutId: number | undefined) => {
  if (!workoutId) return ''
  return props.workouts.find((w) => w.id === workoutId)?.name || ''
}

const isAutoCalculated = (workoutId: number | undefined) => {
  if (!workoutId) return false
  const workout = props.workouts.find((w) => w.id === workoutId)
  return workout?.calculationType === 'Auto' || workout?.calculationType === 'Link'
}

const isLinkedWorkout = (workoutId: number | undefined) => {
  if (!workoutId) return false
  const workout = props.workouts.find((w) => w.id === workoutId)
  return workout?.calculationType === 'Link'
}

const getVatRate = (workoutId: number | undefined) => {
  if (!workoutId) return 0
  const workout = props.workouts.find((w) => w.id === workoutId)
  return ((workout?.vat || 0) * 100).toFixed(1)
}

const calculateVatAmount = (charge: InvoiceCharge) => {
  const workout = props.workouts.find((w) => w.id === charge.workoutId)
  if (!workout) return 0
  return (charge.totalAmount || 0) * (workout.vat || 0)
}

const getDebitCodeName = (debitCodeId: number | undefined) => {
  if (!debitCodeId) return ''
  const debitCode = debitCodes.value.find((d) => d.id === debitCodeId)
  return debitCode ? `${debitCode.code} - ${debitCode.name}` : `DC-${debitCodeId}`
}

const getLinkedWorkoutTooltip = (workoutId: number | undefined) => {
  if (!workoutId) return ''
  const workout = props.workouts.find((w) => w.id === workoutId)
  if (!workout?.linkedWorkoutId) return ''

  const sourceWorkout = props.workouts.find((w) => w.id === workout.linkedWorkoutId)
  return sourceWorkout ? `Linked to: ${sourceWorkout.name}` : ''
}
</script>

<style scoped>
.invoice-category-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
  :deep(.ant-table-wrapper) {
    .ant-table-tbody > tr.ant-table-placeholder {
      height: auto !important;

      .ant-table-cell {
        padding: 8px !important;
      }

      .ant-table-expanded-row-fixed {
        min-height: unset !important;
        height: auto !important;
      }

      .ant-empty.ant-empty-normal {
        margin: 4px 0 !important;

        .ant-empty-image {
          height: 32px !important;
          margin-bottom: 4px !important;

          svg {
            width: 48px !important;
            height: 32px !important;
          }
        }

        .ant-empty-description {
          font-size: 13px !important;
          color: rgba(0, 0, 0, 0.45) !important;
          margin-bottom: 0 !important;
          line-height: 1.2 !important;
        }
      }
    }
  }
}

.category-section {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  min-height: 200px;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafafa;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.delete-category-btn {
  padding: 4px;
  margin-left: 8px;
}

.add-charge-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

.charge-table {
  :deep(.ant-table-thead > tr > th) {
    background: #fafafa;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 12px 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  :deep(.ant-table-cell) {
    .text-ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}
</style>
