<template>
  <div class="cod-management">
    <div class="header">
      <h1>Delivery Code</h1>
      <a-button type="primary" @click="showAddModal">
        <template #icon>
          <PlusOutlined />
        </template>
        Add COD
      </a-button>
    </div>

    <!-- Table -->
    <a-table
      :columns="columns"
      :data-source="filteredCODItems"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'codeType'">
          <a-tag :color="record.codeType === 'Store' ? 'blue' : 'green'">
            {{ record.codeType }}
          </a-tag>
        </template>
        <template v-else-if="column.key === 'actions'">
          <ActionButtons
            name="COD"
            :show-save="false"
            :show-edit="true"
            :show-delete="true"
            :show-view="false"
            @edit="editCOD(record)"
            @delete="deleteCOD(record)"
          />
        </template>
      </template>
    </a-table>

    <!-- Create/Edit Modal -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEditing ? 'Edit COD' : 'Create COD'"
      :confirm-loading="saving"
      :ok-text="isEditing ? 'Update' : 'Create'"
      cancel-text="Cancel"
      @ok="handleSave"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="Code" name="code" required>
          <a-input
            v-model:value="formData.code"
            placeholder="Enter COD code"
            :disabled="isEditing"
          />
        </a-form-item>

        <a-form-item label="Name" name="name">
          <a-input v-model:value="formData.name" placeholder="Enter COD name (optional)" />
        </a-form-item>

        <a-form-item label="Address" name="address">
          <a-input v-model:value="formData.address" placeholder="Enter address (optional)" />
        </a-form-item>

        <a-form-item label="Code Type" name="codeType" required>
          <a-select v-model:value="formData.codeType" placeholder="Select code type">
            <a-select-option value="Store">Store</a-select-option>
            <a-select-option value="Customer">Customer</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { TableColumnsType } from 'ant-design-vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import type { COD, CODFormData, CODFilters } from '../types/COD'
import {
  getCODItems,
  createCOD,
  updateCOD,
  deleteCOD as deleteCODService,
} from '../services/master-data/cod/codService'
import ActionButtons from '../components/ActionButtons.vue'

// Reactive data
const loading = ref(false)
const saving = ref(false)
const deleting = ref(false)
const modalVisible = ref(false)
const isEditing = ref(false)
const editingId = ref<number | null>(null)
const formRef = ref()

const codItems = ref<COD[]>([])
const filters = reactive<CODFilters>({
  search: '',
  codeType: undefined,
})

const formData = reactive<CODFormData>({
  code: '',
  name: '',
  address: '',
  codeType: 'Store',
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
})

// Form validation rules
const rules = {
  code: [
    { required: true, message: 'Please enter COD code' },
    { min: 2, message: 'Code must be at least 2 characters' },
  ],
  codeType: [{ required: true, message: 'Please select code type' }],
}

// Table columns
const columns: TableColumnsType = [
  {
    title: 'Code',
    dataIndex: 'code',
    key: 'code',
    width: 120,
    sorter: (a: COD, b: COD) => a.code.localeCompare(b.code),
  },
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    width: 180,
    sorter: (a: COD, b: COD) => (a.name || '').localeCompare(b.name || ''),
  },
  {
    title: 'Address',
    dataIndex: 'address',
    key: 'address',
    width: 300,
    ellipsis: true,
  },
  {
    title: 'Type',
    dataIndex: 'codeType',
    key: 'codeType',
    width: 120,
    sorter: (a: COD, b: COD) => a.codeType.localeCompare(b.codeType),
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 80,
    align: 'center',
  },
]

// Computed
const filteredCODItems = computed(() => {
  return codItems.value
})

// Methods
const loadCODItems = async (showLoading = true) => {
  try {
    if (showLoading) loading.value = true
    console.log('Loading COD items with filters:', filters)

    const items = await getCODItems(filters)
    codItems.value = items
    pagination.total = items.length

    console.log('COD items loaded:', items.length)
  } catch (error) {
    message.error('Failed to load COD items')
    console.error('Error loading COD items:', error)
  } finally {
    if (showLoading) loading.value = false
  }
}

const showAddModal = () => {
  isEditing.value = false
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

const editCOD = (record: COD) => {
  console.log('Editing COD record:', record)
  isEditing.value = true
  editingId.value = record.id
  formData.code = record.code
  formData.name = record.name || ''
  formData.address = record.address || ''
  formData.codeType = record.codeType
  console.log('Form data set to:', formData)
  modalVisible.value = true
}

const handleSave = async () => {
  try {
    console.log('Starting save process...')
    console.log('Form data:', formData)

    // Manual validation check
    if (!formData.code || formData.code.trim().length < 2) {
      message.error('Please enter a valid code (at least 2 characters)')
      return
    }

    if (!formData.codeType) {
      message.error('Please select a code type')
      return
    }

    // Try form validation
    try {
      await formRef.value?.validate()
      console.log('Form validation passed')
    } catch (validationError) {
      console.log('Form validation failed:', validationError)
      return
    }

    saving.value = true

    if (isEditing.value && editingId.value) {
      console.log('Updating COD:', editingId.value, formData)
      const updatedCOD = await updateCOD(editingId.value, formData)
      console.log('Update result:', updatedCOD)

      // Update local data instead of reloading from database
      const index = codItems.value.findIndex((item) => item.id === editingId.value)
      if (index !== -1) {
        codItems.value[index] = updatedCOD
        console.log('Local data updated successfully')
      }

      message.success('COD updated successfully')
    } else {
      console.log('Creating new COD:', formData)
      const result = await createCOD(formData)
      console.log('Create result:', result)

      // Add new item to local data
      codItems.value.unshift(result)
      pagination.total = codItems.value.length

      message.success('COD created successfully')
    }

    modalVisible.value = false
    resetForm()
  } catch (error) {
    console.error('Save error details:', error)
    message.error(`Failed to save COD: ${error.message || error}`)
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

const resetForm = () => {
  formData.code = ''
  formData.name = ''
  formData.address = ''
  formData.codeType = 'Store'
  formRef.value?.resetFields()
}

const deleteCOD = (record: COD) => {
  Modal.confirm({
    title: 'Delete COD',
    content: `Are you sure you want to delete "${record.code}"?`,
    okText: 'Delete',
    okType: 'danger',
    cancelText: 'Cancel',
    onOk: async () => {
      try {
        deleting.value = true
        console.log('Deleting COD:', record.id, record.code)

        await deleteCODService(record.id)
        console.log('Delete successful, updating local data...')

        // Remove item from local data instead of reloading
        const index = codItems.value.findIndex((item) => item.id === record.id)
        if (index !== -1) {
          codItems.value.splice(index, 1)
          pagination.total = codItems.value.length
          console.log('Local data updated after delete')
        }

        message.success('COD deleted successfully')
      } catch (error) {
        message.error(`Failed to delete COD: ${error.message || error}`)
        console.error('Error deleting COD:', error)
      } finally {
        deleting.value = false
      }
    },
  })
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

// Initialize
onMounted(async () => {
  try {
    loading.value = true
    console.log('Initializing COD Management page...')

    console.log('Loading COD items...')
    await loadCODItems(false)

    console.log('COD Management page initialization completed')
  } catch (error) {
    console.error('Error initializing COD Management page:', error)
    message.error('Failed to initialize page data')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.cod-management {
  padding: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.filters {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-dropdown-menu-item) {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
