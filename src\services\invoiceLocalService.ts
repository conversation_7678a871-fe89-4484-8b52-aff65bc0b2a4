import type { Invoice, InvoiceCategory, InvoiceCharge } from '../types/invoice'
import type { Customer } from '../types/MasterDataTypes/Customer'
import type { Category, Workout, WorkoutCalculationType } from '../types/MasterDataTypes/Category'
import type { Unit } from '../types/MasterDataTypes/Unit'
import type { EntityStatus } from '../types/common'
import { useSQLite } from '@IVC/hooks/useSQLite'
import { getCustomerDivisionsByCustomerId } from './master-data/customer'

interface AutoFunctionQuery {
  autoFocusCode: string
  customerId: number
  divisionId?: number
  yearMonth: string
}

export const useInvoiceService = () => {
  const { executeQuery, tables } = useSQLite()

  // Customer functions
  const getAllCustomers = async (): Promise<Customer[]> => {
    const sql = `
      SELECT id, code, name, address, tax_number, phone, status
      FROM customers
      ORDER BY name
    `
    const result = await executeQuery(sql)
    return (result.result.resultRows?.map((row) => ({
      id: Number(row[0]),
      code: String(row[1]),
      name: String(row[2]),
      address: String(row[3] || ''),
      taxNumber: String(row[4] || ''),
      phone: String(row[5] || ''),
      status: String(row[6] || 'active') as EntityStatus,
    })) || []) as Customer[]
  }

  const getCustomerById = async (id: number): Promise<Customer | null> => {
    const sql = `
      SELECT id, code, name, address, tax_number, phone, status
      FROM customers
      WHERE id = ?
    `
    const result = await executeQuery(sql, [id])
    if (!result.result.resultRows?.length) return null

    const row = result.result.resultRows[0]
    return {
      id: Number(row[0]),
      code: String(row[1]),
      name: String(row[2]),
      address: String(row[3] || ''),
      taxNumber: String(row[4] || ''),
      phone: String(row[5] || ''),
      status: String(row[6] || 'active') as EntityStatus,
    }
  }

  // Category functions
  const getAllCategories = async (): Promise<Category[]> => {
    const sql = `
      SELECT id, code, name, status
      FROM categories
      ORDER BY name
    `
    const result = await executeQuery(sql)
    return (result.result.resultRows?.map((row) => ({
      id: Number(row[0]),
      code: String(row[1]),
      name: String(row[2]),
      status: String(row[3] || 'active') as EntityStatus,
    })) || []) as Category[]
  }

  const getCategoryById = async (id: number): Promise<Category | null> => {
    const sql = `
      SELECT id, code, name, status
      FROM categories
      WHERE id = ?
    `
    const result = await executeQuery(sql, [id])
    if (!result.result.resultRows?.length) return null

    const row = result.result.resultRows[0]
    return {
      id: Number(row[0]),
      code: String(row[1]),
      name: String(row[2]),
      status: String(row[3] || 'active') as EntityStatus,
    }
  }

  // Workout functions
  const getAllWorkouts = async (): Promise<Workout[]> => {
    const sql = `
      SELECT
        id, code, name, category_id, unit_id, unit_price,
        unit_price_sign, unit_price_unit, unit_price_is_percentage,
        status, vat, calculation_type, linked_workout_id,
        linked_auto_focus_code, debit_code_id
      FROM workout
      ORDER BY name
    `
    const result = await executeQuery(sql)
    return (result.result.resultRows?.map((row) => ({
      id: Number(row[0]),
      code: String(row[1]),
      name: String(row[2]),
      categoryId: Number(row[3]),
      unitId: row[4] ? Number(row[4]) : null,
      unitPrice: row[5] ? Number(row[5]) : null,
      unitPriceSign: String(row[6] || '+') as '+' | '-',
      unitPriceUnit: String(row[7] || 'VND') as 'VND' | 'PERCENT',
      unitPriceIsPercentage: Boolean(row[8]),
      status: String(row[9] || 'active') as EntityStatus,
      vat: Number(row[10] || 0),
      calculationType: String(row[11] || 'Manual') as WorkoutCalculationType,
      linkedWorkoutId: row[12] ? Number(row[12]) : null,
      autoFocusCode: row[13] ? String(row[13]) : null,
      debitCodeId: row[14] ? Number(row[14]) : null,
    })) || []) as Workout[]
  }

  const getWorkoutById = async (id: number): Promise<Workout | null> => {
    const sql = `
      SELECT
        id, code, name, category_id, unit_id, unit_price,
        unit_price_sign, unit_price_unit, unit_price_is_percentage,
        status, vat, calculation_type, linked_workout_id,
        linked_auto_focus_code, debit_code_id
      FROM workout
      WHERE id = ?
    `
    const result = await executeQuery(sql, [id])
    if (!result.result.resultRows?.length) return null

    const row = result.result.resultRows[0]
    return {
      id: Number(row[0]),
      code: String(row[1]),
      name: String(row[2]),
      categoryId: Number(row[3]),
      unitId: row[4] ? Number(row[4]) : null,
      unitPrice: row[5] ? Number(row[5]) : null,
      unitPriceSign: String(row[6] || '+') as '+' | '-',
      unitPriceUnit: String(row[7] || 'VND') as 'VND' | 'PERCENT',
      unitPriceIsPercentage: Boolean(row[8]),
      status: String(row[9] || 'active') as EntityStatus,
      vat: Number(row[10] || 0),
      calculationType: String(row[11] || 'Manual') as WorkoutCalculationType,
      linkedWorkoutId: row[12] ? Number(row[12]) : null,
      autoFocusCode: row[13] ? String(row[13]) : null,
      debitCodeId: row[14] ? Number(row[14]) : null,
    }
  }

  const getWorkoutsByCategory = async (categoryId: number): Promise<Workout[]> => {
    const sql = `
      SELECT
        id, code, name, category_id, unit_id, unit_price,
        unit_price_sign, unit_price_unit, unit_price_is_percentage,
        status, vat, calculation_type, linked_workout_id,
        linked_auto_focus_code, debit_code_id
      FROM workout
      WHERE category_id = ?
      ORDER BY name
    `
    const result = await executeQuery(sql, [categoryId])
    return (result.result.resultRows?.map((row) => ({
      id: Number(row[0]),
      code: String(row[1]),
      name: String(row[2]),
      categoryId: Number(row[3]),
      unitId: row[4] ? Number(row[4]) : null,
      unitPrice: row[5] ? Number(row[5]) : null,
      unitPriceSign: String(row[6] || '+') as '+' | '-',
      unitPriceUnit: String(row[7] || 'VND') as 'VND' | 'PERCENT',
      unitPriceIsPercentage: Boolean(row[8]),
      status: String(row[9] || 'active') as EntityStatus,
      vat: Number(row[10] || 0),
      calculationType: String(row[11] || 'Manual') as WorkoutCalculationType,
      linkedWorkoutId: row[12] ? Number(row[12]) : null,
      autoFocusCode: row[13] ? String(row[13]) : null,
      debitCodeId: row[14] ? Number(row[14]) : null,
    })) || []) as Workout[]
  }

  // Unit functions
  const getAllUnits = async (): Promise<Unit[]> => {
    const sql = `
      SELECT id, code, name, status
      FROM units
      ORDER BY name
    `
    const result = await executeQuery(sql)
    return (result.result.resultRows?.map((row) => ({
      id: Number(row[0]),
      code: String(row[1]),
      name: String(row[2]),
      status: String(row[3] || 'active') as EntityStatus,
    })) || []) as Unit[]
  }

  const getUnitById = async (id: number): Promise<Unit | null> => {
    const sql = `
      SELECT id, code, name, status
      FROM units
      WHERE id = ?
    `
    const result = await executeQuery(sql, [id])
    if (!result.result.resultRows?.length) return null

    const row = result.result.resultRows[0]
    return {
      id: Number(row[0]),
      code: String(row[1]),
      name: String(row[2]),
      status: String(row[3] || 'active') as EntityStatus,
    }
  }

  // Invoice functions
  const getAllInvoices = async (): Promise<Invoice[]> => {
    const sql = `
      SELECT
        i.*,
        json_group_array(
          json_object(
            'id', ii.id,
            'categoryId', w.category_id,
            'name', c.name,
            'charges', json_array(
              json_object(
                'id', ii.id,
                'workoutId', w.id,
                'unitId', w.unit_id,
                'unitPrice', ii.unit_price,
                'quantity', ii.quantity,
                'totalAmount', ii.amount,
                'vatRate', ii.vat,
                'vatAmount', ii.vat_amount,
                'debitCodeId', ii.debit_code_id,
                'status', ii.status,
                'createdAt', ii.created_at,
                'updatedAt', ii.updated_at
              )
            )
          )
        ) as categories
      FROM invoices i
      LEFT JOIN invoice_items ii ON i.id = ii.invoice_id
      LEFT JOIN workout w ON ii.workout_id = w.id
      LEFT JOIN categories c ON w.category_id = c.id
      GROUP BY i.id
    `
    const result = await executeQuery(sql)
    return (result.result.resultRows?.map((row) => {
      let categories = []
      try {
        const categoriesJson = row[8] ? JSON.parse(String(row[8])) : []
        // Group charges by category
        const categoryMap = new Map()
        categoriesJson.forEach((item: any) => {
          if (!item || item.categoryId === null) return

          if (!categoryMap.has(item.categoryId)) {
            categoryMap.set(item.categoryId, {
              id: `CAT-${item.categoryId}`,
              categoryId: item.categoryId,
              name: item.name,
              charges: [],
            })
          }

          const category = categoryMap.get(item.categoryId)
          if (item.charges && item.charges.length > 0) {
            category.charges.push(...item.charges)
          }
        })
        categories = Array.from(categoryMap.values())
      } catch (e) {
        console.error('Error parsing categories:', e)
        categories = []
      }

      return {
        id: String(row[0] || ''),
        customerId: row[1] ? Number(row[1]) : undefined,
        customerDivisionId: row[2] ? Number(row[2]) : undefined,
        totalAmount: Number(row[3] || 0),
        status: String(row[4] || 'DRAFT') as Invoice['status'],
        notes: String(row[5] || ''),
        createdAt: String(row[6] || ''),
        updatedAt: String(row[7] || ''),
        categories,
      }
    }) || []) as Invoice[]
  }

  const createInvoice = async (invoice: Invoice): Promise<Invoice> => {
    await executeQuery('BEGIN TRANSACTION')

    try {
      await executeQuery(
        `INSERT INTO invoices (
          id, customer_id, customer_division_id, total_amount,
          status, notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        RETURNING *`,
        [
          invoice.id,
          invoice.customerId,
          invoice.customerDivisionId,
          invoice.totalAmount,
          invoice.status,
          invoice.notes,
          invoice.createdAt,
          invoice.updatedAt,
        ],
      )

      for (const category of invoice.categories) {
        for (const charge of category.charges) {
          await executeQuery(
            `INSERT INTO invoice_items (
              id, invoice_id, category_id, workout_id, unit_id,
              description, quantity, unit_price, amount,
              vat, vat_amount, debit_code_id,
              status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              charge.id,
              invoice.id,
              category.categoryId,
              charge.workoutId,
              charge.unitId,
              category.name,
              charge.quantity,
              charge.unitPrice,
              charge.totalAmount,
              charge.vatRate,
              charge.vatAmount,
              charge.debitCodeId,
              charge.status || invoice.status,
              charge.createdAt || invoice.createdAt,
              charge.updatedAt || invoice.updatedAt,
            ],
          )
        }
      }

      await executeQuery('COMMIT')
      return invoice
    } catch (error) {
      await executeQuery('ROLLBACK')
      throw error
    }
  }

  const updateInvoice = async (id: string, invoice: Partial<Invoice>): Promise<void> => {
    await executeQuery('BEGIN TRANSACTION')

    try {
      if (Object.keys(invoice).length > 1 || !('status' in invoice)) {
        const updateFields = []
        const params = []

        if ('customerId' in invoice) {
          updateFields.push('customer_id = ?')
          params.push(invoice.customerId)
        }
        if ('customerDivisionId' in invoice) {
          updateFields.push('customer_division_id = ?')
          params.push(invoice.customerDivisionId)
        }
        if ('totalAmount' in invoice) {
          updateFields.push('total_amount = ?')
          params.push(invoice.totalAmount)
        }
        if ('status' in invoice) {
          updateFields.push('status = ?')
          params.push(invoice.status)
        }
        if ('notes' in invoice) {
          updateFields.push('notes = ?')
          params.push(invoice.notes)
        }
        if ('updatedAt' in invoice) {
          updateFields.push('updated_at = ?')
          params.push(invoice.updatedAt)
        }

        if (updateFields.length > 0) {
          params.push(id)
          await executeQuery(`UPDATE invoices SET ${updateFields.join(', ')} WHERE id = ?`, params)
        }
      } else if ('status' in invoice) {
        await executeQuery('UPDATE invoices SET status = ? WHERE id = ?', [invoice.status, id])
      }

      if (invoice.categories) {
        await executeQuery('DELETE FROM invoice_items WHERE invoice_id = ?', [id])

        for (const category of invoice.categories) {
          for (const charge of category.charges) {
            await executeQuery(
              `INSERT INTO invoice_items (
                id, invoice_id, category_id, workout_id, unit_id,
                description, quantity, unit_price, amount,
                vat, vat_amount, debit_code_id,
                status, created_at, updated_at
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                charge.id,
                id,
                category.categoryId,
                charge.workoutId,
                charge.unitId,
                category.name,
                charge.quantity,
                charge.unitPrice,
                charge.totalAmount,
                charge.vatRate,
                charge.vatAmount,
                charge.debitCodeId,
                charge.status || invoice.status,
                charge.createdAt || new Date().toISOString(),
                charge.updatedAt || new Date().toISOString(),
              ],
            )
          }
        }
      }

      await executeQuery('COMMIT')
    } catch (error) {
      await executeQuery('ROLLBACK')
      throw error
    }
  }

  const deleteInvoice = async (id: string): Promise<void> => {
    await executeQuery('BEGIN TRANSACTION')
    try {
      await executeQuery('DELETE FROM invoice_items WHERE invoice_id = ?', [id])
      await executeQuery('DELETE FROM invoices WHERE id = ?', [id])
      await executeQuery('COMMIT')
    } catch (error) {
      await executeQuery('ROLLBACK')
      throw error
    }
  }

  const getCustomerDivisions = async (customerId: number) => {
    return getCustomerDivisionsByCustomerId(customerId)
  }

  const getAutoFunctionValue = async (
    query: AutoFunctionQuery,
  ): Promise<{ value: number } | null> => {
    try {
      const { autoFocusCode, customerId, divisionId, yearMonth } = query
      console.log('getAutoFunctionValue -> autoFunctionCode:', autoFocusCode)

      // First get the auto_function_id from code
      const autoFunctionResult = await executeQuery(`SELECT id FROM auto_function WHERE code = ?`, [
        autoFocusCode,
      ])
      console.log('getAutoFunctionValue -> autoFunctionResult:', autoFunctionResult)

      if (!autoFunctionResult?.result?.resultRows?.length) {
        console.error('Auto function not found for code:', autoFocusCode)
        return null
      }

      const autoFunctionId = autoFunctionResult.result.resultRows[0][0]

      let sql = `
        SELECT value
        FROM auto_function_customer_detail
        WHERE auto_function_id = ?
        AND customer_id = ?
        AND year_month = ?
      `
      const params = [autoFunctionId, customerId, yearMonth]

      if (divisionId) {
        sql += ' AND division_id = ?'
        params.push(divisionId)
      } else {
        sql += ' AND division_id IS NULL'
      }

      sql += ' ORDER BY created_at DESC LIMIT 1'

      console.log('getAutoFunctionValue -> sql:', sql)
      console.log('getAutoFunctionValue -> params:', params)

      const response = await executeQuery(sql, params)
      if (response?.result?.resultRows && response.result.resultRows.length > 0) {
        return { value: response.result.resultRows[0][0] as number }
      }
      return null
    } catch (error) {
      console.error('Error getting auto function value:', error)
      throw error
    }
  }

  return {
    getAllCustomers,
    getCustomerById,
    getAllCategories,
    getCategoryById,
    getAllWorkouts,
    getWorkoutById,
    getWorkoutsByCategory,
    getAllUnits,
    getUnitById,
    getAllInvoices,
    createInvoice,
    updateInvoice,
    deleteInvoice,
    getAutoFunctionValue,
    getCustomerDivisions,
  }
}
