<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSQLite } from '@IVC/hooks/useSQLite'
import UnitForm from '@IVC/components/dialogs/UnitForm.vue'
import { Modal, message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import ActionButtons from '@IVC/components/ActionButtons.vue'
import { type Unit, type EntityStatus } from '@IVC/types/MasterDataTypes/Unit'
import { getAllUnits, addUnit, updateUnit, deleteUnit } from '@IVC/services/master-data/unit' // Assuming Unit services

const { isLoading } = useSQLite()

const units = ref<Unit[] | null>([])
const isModalVisible = ref(false)
const editingUnit = ref<Unit | null>(null)
const unitFormRef = ref<InstanceType<typeof UnitForm> | null>(null)

const columns = [
  {
    title: 'Code',
    dataIndex: 'code',
    key: 'code',
    sorter: (a: Unit, b: Unit) => a.code.localeCompare(b.code),
  },
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    sorter: (a: Unit, b: Unit) => a.name.localeCompare(b.name),
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  { title: 'Action', key: 'action', width: 150 }, // Adjusted width for typical actions
]

const fetchUnits = async () => {
  units.value = null // Set to null to indicate loading if table handles it, or keep empty array
  try {
    const fetchedUnits = await getAllUnits()
    units.value = fetchedUnits
  } catch (err) {
    message.error(`Failed to load units: ${(err as Error).message}`)
    console.error(err)
  }
}

onMounted(() => {
  fetchUnits()
})

const showAddModal = () => {
  editingUnit.value = null
  isModalVisible.value = true
}

const showEditModal = (unit: Unit) => {
  editingUnit.value = { ...unit } // Create a copy to avoid direct mutation
  isModalVisible.value = true
}

const handleModalClose = () => {
  isModalVisible.value = false
  editingUnit.value = null
  unitFormRef.value?.resetFormAndLoading() // Assuming UnitForm has this method
}

const handleModalSave = async (unitData: Partial<Unit>) => {
  try {
    if (unitData.id) {
      // Editing existing unit
      await updateUnit(unitData as Unit) // Cast needed as id is present
      message.success('Unit updated successfully!')
    } else {
      // Adding new unit
      await addUnit(unitData)
      message.success('Unit added successfully!')
    }
    await fetchUnits() // Refresh the list
    handleModalClose()
  } catch (err) {
    message.error(`Failed to save unit: ${(err as Error).message}`)
    console.error(err)
  } finally {
    unitFormRef.value?.resetFormAndLoading() // Ensure loading state is reset
  }
}

const confirmDeleteUnit = (id: number) => {
  Modal.confirm({
    title: 'Are you sure you want to delete this unit?',
    content: 'This action will permanently delete the unit data.',
    okText: 'Delete',
    okType: 'danger',
    cancelText: 'Cancel',
    async onOk() {
      await handleDelete(id)
    },
  })
}

const handleDelete = async (id: number) => {
  try {
    await deleteUnit(id)
    message.success('Unit deleted successfully!')
    await fetchUnits() // Refresh the list
  } catch (err) {
    const errorMessage = (err as Error).message || 'Unknown error'
    message.error(`Failed to delete unit: ${errorMessage}`)
    console.error(err)
  }
}

const handleSetUnitStatus = async (unit: Unit, newStatus: EntityStatus) => {
  try {
    await updateUnit({ id: unit.id, status: newStatus })
    message.success(`Unit "${unit.name}" status updated to ${newStatus}.`)
    await fetchUnits() // Refresh the list to show updated status
  } catch (err) {
    message.error(`Failed to update status for unit "${unit.name}": ${(err as Error).message}`)
    console.error(err)
  }
}

// Handler for the inline status switch in the table
const handleInlineStatusChange = async (unitToUpdate: Unit, isChecked: boolean) => {
  const newStatus: EntityStatus = isChecked ? 'active' : 'inactive'
  const oldStatus = unitToUpdate.status

  // Find the unit in the local array to update its status for UI reactivity
  // This ensures the switch visually updates immediately (optimistic update)
  const unitInList = units.value?.find((u) => u.id === unitToUpdate.id)
  if (!unitInList) {
    message.error('Unit not found in local list to update status.')
    return
  }

  unitInList.status = newStatus // Optimistic UI update

  try {
    await updateUnit({ id: unitToUpdate.id, status: newStatus })
    message.success(`Unit "${unitToUpdate.name}" status updated to ${newStatus}.`)
    // Optionally, you could call fetchUnits() here if you want to ensure the data is perfectly synced
    // with the backend, but for a simple status toggle, optimistic update is often sufficient.
  } catch (err) {
    message.error(
      `Failed to update status for unit "${unitToUpdate.name}": ${(err as Error).message}`,
    )
    // Revert the status in the local list if the update fails
    if (unitInList) {
      unitInList.status = oldStatus
    }
    console.error(err)
  }
}
</script>

<template>
  <div>
    <a-page-header title="Unit Management" sub-title="Manage your unit details">
      <template #extra>
        <a-button type="primary" @click="showAddModal"> <PlusOutlined /> Add Unit </a-button>
      </template>
    </a-page-header>

    <a-table :columns="columns" :data-source="units" :loading="isLoading" row-key="id">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <ActionButtons
            name="Unit"
            :record-status="record.status"
            :show-edit="true"
            :show-delete="true"
            :show-set-status="true"
            :show-add-customer-divisions="false"
            :show-manage-divisions="false"
            :show-setting-button="false"
            :show-save="false"
            @edit="showEditModal(record)"
            @delete="confirmDeleteUnit(record.id)"
            @set-status="(newStatus: EntityStatus) => handleSetUnitStatus(record, newStatus)"
          />
        </template>
        <template v-else-if="column.key === 'status'">
          <!-- Inline status switch -->
          <a-switch
            :checked="record.status === 'active'"
            @change="(checked: boolean) => handleInlineStatusChange(record, checked)"
            checked-children="Active"
            un-checked-children="Inactive"
            style="min-width: 70px"
          />
        </template>
      </template>
    </a-table>

    <UnitForm
      ref="unitFormRef"
      :visible="isModalVisible"
      :unit-data="editingUnit"
      @close="handleModalClose"
      @save="handleModalSave"
    />
  </div>
</template>

<style scoped></style>
