import { useSQLite } from '@IVC/hooks/useSQLite'
import { type User } from '@IVC/types/MasterDataTypes/User'
import { message } from 'ant-design-vue'

export async function getAllUsers(): Promise<User[]> {
  const { executeQuery, tables } = useSQLite()
  // The query selects columns in the order: id, username, password, email, role, name, status
  const query = `SELECT id, username, password, email, role, name, status FROM ${tables.users} ORDER BY id DESC`

  try {
    const response = await executeQuery(query)
    // Ensure response and resultRows exist and are in the expected format
    if (response?.result?.resultRows && Array.isArray(response.result.resultRows)) {
      // Map each row (which is an array of values) to a User object
      return response.result.resultRows.map((row: unknown[]) => {
        return {
          id: row[0] as number,
          username: row[1] as string,
          password: row[2] as string,
          email: row[3] as string,
          role: row[4] as 'admin' | 'manager' | 'user',
          name: row[5] as string,
          status: Boolean(row[6]),
        } as User
      })
    }
    return [] // Return an empty array if data is not in the expected format
  } catch (err) {
    console.error('Failed to get users:', err)
    throw err // Re-throw the error to be handled by the caller
  }
}

export async function getUserById(id: number): Promise<User | null> {
  const { executeQuery, tables } = useSQLite()
  const query = `SELECT id, username, password, email, role, name, status FROM ${tables.users} WHERE id = ?`

  try {
    const response = await executeQuery(query, [id])
    if (
      response?.result?.resultRows &&
      Array.isArray(response.result.resultRows) &&
      response.result.resultRows.length > 0
    ) {
      const row = response.result.resultRows[0]
      return {
        id: row[0] as number,
        username: row[1] as string,
        password: row[2] as string,
        email: row[3] as string,
        role: row[4] as 'admin' | 'manager' | 'user',
        name: row[5] as string,
        status: Boolean(row[6]),
      } as User
    }
    return null
  } catch (err) {
    console.error('Failed to get user:', err)
    throw err
  }
}

export async function addUser(userData: Partial<User>): Promise<void> {
  const { executeQuery, tables } = useSQLite()

  // Check if username already exists
  const existingUsername = await getUserByUsername(userData.username!)
  if (existingUsername) {
    message.error('Username already exists')
    throw new Error('Username already exists')
  }

  // Check if email already exists
  const existingEmail = await getUserByEmail(userData.email!)
  if (existingEmail) {
    message.error('Email already exists')
    throw new Error('Email already exists')
  }

  const query = `
    INSERT INTO ${tables.users} (username, password, email, role, name, status)
    VALUES (?, ?, ?, ?, ?, ?);
  `
  const params = [
    userData.username,
    userData.password,
    userData.email,
    userData.role || 'user',
    userData.name,
    userData.status ? 1 : 0,
  ]

  try {
    await executeQuery(query, params)
    message.success('User created successfully!')
  } catch (err) {
    console.error('Failed to add user:', err)
    message.error('Failed to create user')
    throw err
  }
}

export async function updateUser(user: Partial<User>): Promise<void> {
  const { executeQuery, tables } = useSQLite()

  // Check if username already exists (excluding current user)
  const existingUsername = await getUserByUsername(user.username!)
  if (existingUsername && existingUsername.id !== user.id) {
    message.error('Username already exists')
    throw new Error('Username already exists')
  }

  // Check if email already exists (excluding current user)
  const existingEmail = await getUserByEmail(user.email!)
  if (existingEmail && existingEmail.id !== user.id) {
    message.error('Email already exists')
    throw new Error('Email already exists')
  }

  const query = `
    UPDATE ${tables.users}
    SET username = ?, password = ?, email = ?, role = ?, name = ?, status = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?;
  `
  const params = [
    user.username,
    user.password,
    user.email,
    user.role,
    user.name,
    user.status ? 1 : 0,
    user.id,
  ]

  try {
    await executeQuery(query, params)
    message.success('User updated successfully!')
  } catch (err) {
    console.error(`Failed to update user with ID ${user.id}:`, err)
    message.error('Failed to update user')
    throw err
  }
}

export async function deleteUser(id: number): Promise<void> {
  const { executeQuery, tables } = useSQLite()

  // Get user name before deletion for message
  const user = await getUserById(id)
  const userName = user?.name || 'User'

  const query = `DELETE FROM ${tables.users} WHERE id = ?;`
  const params = [id]

  try {
    await executeQuery(query, params)
    message.success(`${userName} has been deleted successfully!`)
  } catch (err) {
    console.error(`Failed to delete user with ID ${id}:`, err)
    message.error('Failed to delete user')
    throw err
  }
}

export async function updateUserStatus(id: number, status: boolean): Promise<void> {
  const { executeQuery, tables } = useSQLite()

  // Get user name for message
  const user = await getUserById(id)
  const userName = user?.name || 'User'

  const query = `UPDATE ${tables.users} SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?;`
  const params = [status ? 1 : 0, id]

  try {
    await executeQuery(query, params)
    const statusText = status ? 'activated' : 'deactivated'
    message.success(`${userName} has been ${statusText}`)
  } catch (err) {
    console.error(`Failed to update user status with ID ${id}:`, err)
    message.error('Failed to update user status')
    throw err
  }
}

export async function getUserByUsername(username: string): Promise<User | null> {
  const { executeQuery, tables } = useSQLite()
  const query = `SELECT id, username, password, email, role, name, status FROM ${tables.users} WHERE username = ?`

  try {
    const response = await executeQuery(query, [username])
    if (
      response?.result?.resultRows &&
      Array.isArray(response.result.resultRows) &&
      response.result.resultRows.length > 0
    ) {
      const row = response.result.resultRows[0]
      return {
        id: row[0] as number,
        username: row[1] as string,
        password: row[2] as string,
        email: row[3] as string,
        role: row[4] as 'admin' | 'manager' | 'user',
        name: row[5] as string,
        status: Boolean(row[6]),
      } as User
    }
    return null
  } catch (err) {
    console.error('Failed to get user by username:', err)
    return null
  }
}

export async function getUserByEmail(email: string): Promise<User | null> {
  const { executeQuery, tables } = useSQLite()
  const query = `SELECT id, username, password, email, role, name, status FROM ${tables.users} WHERE email = ?`

  try {
    const response = await executeQuery(query, [email])
    if (
      response?.result?.resultRows &&
      Array.isArray(response.result.resultRows) &&
      response.result.resultRows.length > 0
    ) {
      const row = response.result.resultRows[0]
      return {
        id: row[0] as number,
        username: row[1] as string,
        password: row[2] as string,
        email: row[3] as string,
        role: row[4] as 'admin' | 'manager' | 'user',
        name: row[5] as string,
        status: Boolean(row[6]),
      } as User
    }
    return null
  } catch (err) {
    console.error('Failed to get user by email:', err)
    return null
  }
}

export async function searchUsers(query: string): Promise<User[]> {
  const { executeQuery, tables } = useSQLite()
  const searchQuery = `
    SELECT id, username, password, email, role, name, status
    FROM ${tables.users}
    WHERE name LIKE ? OR username LIKE ? OR email LIKE ?
    ORDER BY id DESC
  `
  const searchTerm = `%${query}%`

  try {
    const response = await executeQuery(searchQuery, [searchTerm, searchTerm, searchTerm])
    if (response?.result?.resultRows && Array.isArray(response.result.resultRows)) {
      return response.result.resultRows.map((row: unknown[]) => {
        return {
          id: row[0] as number,
          username: row[1] as string,
          password: row[2] as string,
          email: row[3] as string,
          role: row[4] as 'admin' | 'manager' | 'user',
          name: row[5] as string,
          status: Boolean(row[6]),
        } as User
      })
    }
    return []
  } catch (err) {
    console.error('Failed to search users:', err)
    throw err
  }
}

export async function getUsersByRole(role: 'admin' | 'manager' | 'user'): Promise<User[]> {
  const { executeQuery, tables } = useSQLite()
  const query = `SELECT id, username, password, email, role, name, status FROM ${tables.users} WHERE role = ? ORDER BY id DESC`

  try {
    const response = await executeQuery(query, [role])
    if (response?.result?.resultRows && Array.isArray(response.result.resultRows)) {
      return response.result.resultRows.map((row: unknown[]) => {
        return {
          id: row[0] as number,
          username: row[1] as string,
          password: row[2] as string,
          email: row[3] as string,
          role: row[4] as 'admin' | 'manager' | 'user',
          name: row[5] as string,
          status: Boolean(row[6]),
        } as User
      })
    }
    return []
  } catch (err) {
    console.error('Failed to get users by role:', err)
    throw err
  }
}

export async function sendInvitation(id: number): Promise<void> {
  const user = await getUserById(id)
  if (!user) {
    message.error('User not found')
    throw new Error('User not found')
  }

  // Simulate sending invitation
  await new Promise((resolve) => setTimeout(resolve, 400))
  message.info(`Invitation sent to ${user.name}`)
}

export async function authenticateUser(username: string, password: string): Promise<User | null> {
  const { executeQuery, tables } = useSQLite()

  // Try to find user by username or email
  const query = `
    SELECT id, username, password, email, role, name, status
    FROM ${tables.users}
    WHERE (username = ? OR email = ?) AND status = 1
  `

  try {
    const response = await executeQuery(query, [username, username])
    if (
      response?.result?.resultRows &&
      Array.isArray(response.result.resultRows) &&
      response.result.resultRows.length > 0
    ) {
      const row = response.result.resultRows[0]
      const user = {
        id: row[0] as number,
        username: row[1] as string,
        password: row[2] as string,
        email: row[3] as string,
        role: row[4] as 'admin' | 'manager' | 'user',
        name: row[5] as string,
        status: Boolean(row[6]),
      } as User

      // Check password (in real app, this should be hashed)
      if (user.password === password) {
        return user
      }
    }
    return null
  } catch (err) {
    console.error('Failed to authenticate user:', err)
    return null
  }
}

export async function changeUserPassword(
  userId: number,
  currentPassword: string,
  newPassword: string,
): Promise<void> {
  const { executeQuery, tables } = useSQLite()

  try {
    // First, verify the current password
    const user = await getUserById(userId)
    if (!user) {
      message.error('User not found')
      throw new Error('User not found')
    }

    if (user.password !== currentPassword) {
      message.error('Current password is incorrect')
      throw new Error('Current password is incorrect')
    }

    // Update the password
    const updateQuery = `
      UPDATE ${tables.users}
      SET password = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `

    await executeQuery(updateQuery, [newPassword, userId])
    message.success('Password changed successfully!')
  } catch (err) {
    console.error('Failed to change password:', err)
    if (err instanceof Error) {
      throw err
    } else {
      message.error('Failed to change password')
      throw new Error('Failed to change password')
    }
  }
}

export async function seedInitialUsers(): Promise<void> {
  const { executeQuery, tables } = useSQLite()

  // Check if users already exist
  const checkQuery = `SELECT COUNT(*) as count FROM ${tables.users}`
  try {
    const response = await executeQuery(checkQuery)
    const count = response?.result?.resultRows?.[0]?.[0] as number

    if (count > 0) {
      console.log('Users already exist, skipping seed')
      return
    }

    // Insert initial users
    const initialUsers = [
      {
        username: 'admin',
        password: 'admin123',
        email: '<EMAIL>',
        role: 'admin',
        name: 'Administrator',
        status: 1,
      },
      {
        username: 'manager',
        password: 'manager123',
        email: '<EMAIL>',
        role: 'manager',
        name: 'Manager User',
        status: 1,
      },
      {
        username: 'user',
        password: 'user123',
        email: '<EMAIL>',
        role: 'user',
        name: 'Regular User',
        status: 1,
      },
    ]

    const insertQuery = `
      INSERT INTO ${tables.users} (username, password, email, role, name, status)
      VALUES (?, ?, ?, ?, ?, ?)
    `

    for (const user of initialUsers) {
      await executeQuery(insertQuery, [
        user.username,
        user.password,
        user.email,
        user.role,
        user.name,
        user.status,
      ])
    }

    console.log('Initial users seeded successfully')
  } catch (err) {
    console.error('Failed to seed initial users:', err)
  }
}
