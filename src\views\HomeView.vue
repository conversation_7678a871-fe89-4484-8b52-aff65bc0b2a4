<script setup lang="ts">
import HelloWorld from '@IVC/components/HelloWorld.vue'
import { useSQLite } from '@IVC/hooks/useSQLite'
import { ref } from 'vue'

const { isLoading, error, executeQuery, isInitialized, initialize } = useSQLite()

const sqlQuery = ref('SELECT * FROM users')
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const queryResult = ref<any[]>([])
const columnNames = ref<string[]>([])
const queryError = ref<string | null>(null)

async function initializeDatabase() {
  try {
    queryError.value = null
    await initialize()
    console.log('Database initialized successfully')
  } catch (err) {
    console.error('Database initialization error:', err)
    queryError.value = err instanceof Error ? err.message : 'Database initialization failed'
  }
}

const exampleQueries = [
  { title: 'Select all', query: 'SELECT * FROM test_table' },
  { title: 'Select users', query: 'SELECT * FROM test_table' },
  { title: 'Select inbound', query: 'SELECT * FROM customer_inbound' },
  { title: 'Insert', query: "INSERT INTO test_table (name) VALUES ('New Test Item')" },
  {
    title: 'Insert Inbound',
    query: `INSERT INTO customer_inbound (
    customer_id, avr_as_num, avh_arv_ymd, avr_prod_cod, avd_prod_nam, 
    avr_rtpc_qty, prod_ppc_num, prod_hrc1, ai, inner_master, 
    carton, pcs, inner_carton, inner_pcs, total_carton, 
    total_pcs, total_ai, total_m3
  ) VALUES (
    1, 'ASN001', '2024-01-15', 12345, 'Sample Product A', 
    100, 67890, 'HRC001', 50, 10, 
    5, 500, 2, 250, 15, 
    750, 150, 2.5
  )`,
  },
  { title: 'Update', query: "UPDATE test_table SET name = 'Updated Item' WHERE name LIKE 'New%'" },
  { title: 'Delete', query: "DELETE FROM test_table WHERE name = 'Updated Item'" },
]

async function runQuery() {
  queryError.value = null
  queryResult.value = []
  columnNames.value = []

  try {
    console.log('Executing query:', sqlQuery.value)
    const result = await executeQuery(sqlQuery.value)
    const isSelect = sqlQuery.value.trim().toLowerCase().startsWith('select')

    console.log('Full result:', result)
    console.log('Result type:', result?.type)
    console.log('Result data:', result?.result)

    if (isSelect) {
      const rows = result?.result?.resultRows || []
      console.log('Rows from result:', rows)

      // Always try to get column names from PRAGMA, even if no data
      const tableMatch = sqlQuery.value.match(/from\s+(\w+)/i)
      if (tableMatch) {
        const tableName = tableMatch[1]
        try {
          const pragmaResult = await executeQuery(`PRAGMA table_info(${tableName})`)
          console.log('PRAGMA result:', pragmaResult)
          const pragmaRows = pragmaResult?.result?.resultRows || []
          columnNames.value = pragmaRows.map((row: unknown[]) => String(row[1]))
        } catch (e) {
          console.warn('Could not get table info:', e)
        }
      }

      // Convert to objects
      if (rows.length > 0) {
        if (columnNames.value.length > 0) {
          queryResult.value = rows.map((row: unknown[]) => {
            const obj: Record<string, unknown> = {}
            columnNames.value.forEach((colName, index) => {
              obj[colName] = row[index]
            })
            return obj
          })
        } else {
          // Fallback: use indices as column names
          queryResult.value = rows.map((row: unknown[]) => {
            const obj: Record<string, unknown> = {}
            row.forEach((value, index) => {
              obj[`column_${index}`] = value
            })
            return obj
          })
        }
      } else {
        // No rows but we still want to show empty table with headers
        queryResult.value = []
      }

      console.log('Column Names:', columnNames.value)
      console.log('Final Query Result:', queryResult.value)
    } else {
      // After mutation, refresh test_table data
      const selectResult = await executeQuery('SELECT * FROM test_table')
      const rows = selectResult?.result?.resultRows || []

      if (rows.length > 0) {
        try {
          const pragmaResult = await executeQuery('PRAGMA table_info(test_table)')
          const pragmaRows = pragmaResult?.result?.resultRows || []
          columnNames.value = pragmaRows.map((row: unknown[]) => String(row[1]))
        } catch (e) {
          console.warn('Could not get table info:', e)
        }

        if (columnNames.value.length > 0) {
          queryResult.value = rows.map((row: unknown[]) => {
            const obj: Record<string, unknown> = {}
            columnNames.value.forEach((colName, index) => {
              obj[colName] = row[index]
            })
            return obj
          })
        } else {
          queryResult.value = rows.map((row: unknown[]) => {
            const obj: Record<string, unknown> = {}
            row.forEach((value, index) => {
              obj[`column_${index}`] = value
            })
            return obj
          })
        }
      }
    }
  } catch (err) {
    console.error('Query error:', err)
    queryError.value = err instanceof Error ? err.message : 'An error occurred'
  }
}
</script>

<template>
  <main>
    <div
      style="
        max-width: 80rem;
        margin-left: auto;
        margin-right: auto;
        padding-left: 1rem;
        padding-right: 1rem;
        padding-top: 1.5rem;
        padding-bottom: 1.5rem;
      "
    >
      <div style="margin-bottom: 24px">
        <h2 style="font-size: 1.5rem; line-height: 2rem; font-weight: 700">Store Usage Sample</h2>
        <HelloWorld />
      </div>
      <div>
        <h2 style="font-size: 1.5rem; line-height: 2rem; font-weight: 700">SQLite Playground</h2>

        <!-- Example queries -->
        <div style="margin-top: 1rem">
          <h3 style="font-size: 0.875rem; line-height: 1.25rem; font-weight: 500">
            Example Queries:
          </h3>
          <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem; flex-wrap: wrap">
            <a-button
              style="
                padding-left: 0.75rem;
                padding-right: 0.75rem;
                padding-top: 0.25rem;
                padding-bottom: 0.25rem;
                font-size: 0.875rem;
                line-height: 1.25rem;
                border-radius: 9999px;
                background-color: #dc2626;
                color: white;
                border: none;
              "
              :disabled="isLoading"
              @click="initializeDatabase"
            >
              {{ isInitialized ? 'Initialized ✓' : 'Initialize' }}
            </a-button>
            <a-button
              v-for="example in exampleQueries"
              :key="example.title"
              style="
                padding-left: 0.75rem;
                padding-right: 0.75rem;
                padding-top: 0.25rem;
                padding-bottom: 0.25rem;
                font-size: 0.875rem;
                line-height: 1.25rem;
                border-radius: 9999px;
                background-color: #f3f4f6;
              "
              @click="sqlQuery = example.query"
            >
              {{ example.title }}
            </a-button>
          </div>
        </div>

        <!-- Query input -->
        <div style="margin-top: 1.5rem">
          <a-textarea
            v-model:value="sqlQuery"
            rows="4"
            style="
              width: 100%;
              padding-left: 1rem;
              padding-right: 1rem;
              padding-top: 0.75rem;
              padding-bottom: 0.75rem;
              border-radius: 0.5rem;
              font-family:
                ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
                'Courier New', monospace;
              font-size: 0.875rem;
              line-height: 1.25rem;
            "
            :disabled="isLoading"
          />
          <a-button
            :disabled="isLoading"
            type="primary"
            style="margin-top: 0.5rem; border-radius: 0.5rem"
            @click="runQuery"
          >
            {{ isLoading ? 'Running...' : 'Run Query' }}
          </a-button>
        </div>

        <!-- Error display -->
        <div
          v-if="error || queryError"
          style="
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: #fef2f2;
            color: #dc2626;
          "
        >
          {{ error?.message || queryError }}
        </div>

        <!-- Results table -->
        <div v-if="queryResult.length > 0 || columnNames.length > 0" style="margin-top: 1rem">
          <h3 style="font-size: 1.125rem; line-height: 1.75rem; font-weight: 600">Results:</h3>
          <div style="margin-top: 0.5rem; overflow-x: auto">
            <table style="width: 100%; border-collapse: collapse; border: 1px solid #e5e7eb">
              <thead>
                <tr style="background-color: #f9fafb">
                  <th
                    v-for="column in columnNames.length > 0
                      ? columnNames
                      : queryResult.length > 0
                        ? Object.keys(queryResult[0])
                        : []"
                    :key="column"
                    style="
                      padding-left: 1rem;
                      padding-right: 1rem;
                      padding-top: 0.75rem;
                      padding-bottom: 0.75rem;
                      text-align: left;
                      font-weight: 600;
                      border: 1px solid #e5e7eb;
                      background-color: #f3f4f6;
                    "
                  >
                    {{ column }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr v-if="queryResult.length === 0 && columnNames.length > 0">
                  <td
                    :colspan="columnNames.length"
                    style="
                      padding-left: 1rem;
                      padding-right: 1rem;
                      padding-top: 1rem;
                      padding-bottom: 1rem;
                      text-align: center;
                      color: #6b7280;
                      font-style: italic;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    No data found
                  </td>
                </tr>
                <tr
                  v-else
                  v-for="(row, index) in queryResult"
                  :key="index"
                  style="border-bottom: 1px solid #e5e7eb"
                >
                  <td
                    v-for="column in Object.keys(row)"
                    :key="column"
                    style="
                      padding-left: 1rem;
                      padding-right: 1rem;
                      padding-top: 0.5rem;
                      padding-bottom: 0.5rem;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    {{ row[column] }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>
