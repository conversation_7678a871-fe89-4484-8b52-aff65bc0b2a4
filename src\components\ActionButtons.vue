<template>
  <a-dropdown :trigger="['click']" placement="bottomRight">
    <a-button type="text" size="small" class="action-btn more-btn">
      <MoreOutlined />
    </a-button>
    <template #overlay>
      <a-menu @click="handleMenuClick">
        <a-menu-item v-if="props.showSave" key="save">
          <SaveOutlined />
          <span style="margin-left: 8px">Save {{ props.name }}</span>
        </a-menu-item>
        <a-menu-item v-else-if="props.showEdit" key="edit">
          <EditOutlined />
          <span style="margin-left: 8px">Edit {{ props.name }}</span>
        </a-menu-item>
        <a-menu-item v-if="props.showView" key="view">
          <EyeOutlined />
          <span style="margin-left: 8px">View {{ props.name }}</span>
        </a-menu-item>
        <a-menu-item v-if="props.showDownload" key="download">
          <DownloadOutlined />
          <span style="margin-left: 8px">Download {{ props.name }}</span>
        </a-menu-item>
        <a-menu-item v-if="props.showAddCustomerDivisions" key="showAddCustomerDivisions">
          <PlusOutlined />
          <span style="margin-left: 8px">Add {{ props.name }} Divisions</span>
        </a-menu-item>
        <a-menu-item v-if="props.showSettingButton" key="setting">
          <SettingOutlined />
          <span style="margin-left: 8px">Settings</span>
        </a-menu-item>
        <a-menu-item v-if="props.showAddWorkout" key="addWorkout">
          <AppstoreAddOutlined />
          <span style="margin-left: 8px">Add Workout</span>
        </a-menu-item>
        <a-menu-item v-if="props.showWorkoutCharge" key="workoutCharge">
          <AppstoreAddOutlined />
          <span style="margin-left: 8px">Monthly Charge</span>
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item v-if="props.showDelete" key="delete" class="danger-menu-item">
          <DeleteOutlined />
          <span style="margin-left: 8px">Delete {{ props.name }}</span>
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import {
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  PlusOutlined,
  SettingOutlined,
  AppstoreAddOutlined,
  SaveOutlined,
  EyeOutlined,
  DownloadOutlined,
} from '@ant-design/icons-vue'

interface Props {
  showSave?: boolean
  showEdit?: boolean
  showDelete?: boolean
  showView?: boolean
  showDownload?: boolean
  showAddCustomerDivisions?: boolean
  showSettingButton?: boolean
  showAddWorkout?: boolean
  showWorkoutCharge?: boolean
  name: string
}

const props = withDefaults(defineProps<Props>(), {
  showSave: false,
  showEdit: false,
  showDelete: false,
  showView: false,
  showDownload: false,
  showAddCustomerDivisions: false,
  showSettingButton: false,
  showAddWorkout: false,
  showWorkoutCharge: false,
})

interface Emits {
  (e: 'edit'): void
  (e: 'save'): void
  (e: 'delete'): void
  (e: 'view'): void
  (e: 'download'): void
  (e: 'addCustomerDivisions'): void
  (e: 'setting'): void
  (e: 'addWorkout'): void
  (e: 'workoutCharge'): void
}

const emit = defineEmits<Emits>()

const handleMenuClick = (info: { key: string | number }) => {
  if (info.key === 'edit' && props.showEdit) {
    emit('edit')
  } else if (info.key === 'save' && props.showSave) {
    emit('save')
  } else if (info.key === 'delete' && props.showDelete) {
    emit('delete')
  } else if (info.key === 'view' && props.showView) {
    emit('view')
  } else if (info.key === 'download' && props.showDownload) {
    emit('download')
  } else if (info.key === 'showAddCustomerDivisions' && props.showAddCustomerDivisions) {
    emit('addCustomerDivisions')
  } else if (info.key === 'setting' && props.showSettingButton) {
    emit('setting')
  } else if (info.key === 'addWorkout' && props.showAddWorkout) {
    emit('addWorkout')
  } else if (info.key === 'workoutCharge' && props.showWorkoutCharge) {
    emit('workoutCharge')
  }
}
</script>

<style scoped>
:deep(.danger-menu-item:hover) {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}
</style>
