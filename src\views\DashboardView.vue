<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Card, Row, Col, Statistic, Table, Button } from 'ant-design-vue'
import { DollarCircleOutlined, FileTextOutlined, WarningOutlined } from '@ant-design/icons-vue'
import { use } from 'echarts/core'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
} from 'echarts/components'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import type { Invoice } from '@IVC/types/invoice'

// Register ECharts components
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LabelLayout,
  UniversalTransition,
])

const pendingInvoices = ref(8)

// Stock charges by customer with monthly data
const stockCharges = ref([
  {
    customer: 'Customer A',
    totalCharge: 285000.0,
    monthlyData: [
      { month: 'Jan', amount: 25000 },
      { month: 'Feb', amount: 27500 },
      { month: 'Mar', amount: 24800 },
      { month: 'Apr', amount: 23500 },
      { month: 'May', amount: 22000 },
      { month: 'Jun', amount: 24000 },
      { month: 'Jul', amount: 25500 },
      { month: 'Aug', amount: 23000 },
      { month: 'Sep', amount: 24500 },
      { month: 'Oct', amount: 23200 },
      { month: 'Nov', amount: 22000 },
      { month: 'Dec', amount: 20000 },
    ],
  },
  {
    customer: 'Customer B',
    totalCharge: 255000.0,
    monthlyData: [
      { month: 'Jan', amount: 20000 },
      { month: 'Feb', amount: 22500 },
      { month: 'Mar', amount: 23800 },
      { month: 'Apr', amount: 21500 },
      { month: 'May', amount: 20000 },
      { month: 'Jun', amount: 22000 },
      { month: 'Jul', amount: 23500 },
      { month: 'Aug', amount: 21000 },
      { month: 'Sep', amount: 22500 },
      { month: 'Oct', amount: 21200 },
      { month: 'Nov', amount: 20000 },
      { month: 'Dec', amount: 17000 },
    ],
  },
])

interface ExtendedInvoice extends Invoice {
  invoiceNumber: string
}

const recentInvoices = ref<ExtendedInvoice[]>([
  {
    id: 'INV-2024-001',
    invoiceNumber: 'INV/2024/03/001',
    customerId: 1,
    simulationId: 'SIM-001',
    invoiceDate: '2024-03-15',
    dueDate: '2024-04-14',
    status: 'PENDING',
    subtotal: 24800.0,
    tax: 1736.0,
    totalAmount: 26536.0,
    createdAt: '2024-03-15',
    updatedAt: '2024-03-15',
  },
  {
    id: 'INV-2024-002',
    invoiceNumber: 'INV/2024/03/002',
    customerId: 2,
    simulationId: 'SIM-002',
    invoiceDate: '2024-03-14',
    dueDate: '2024-04-13',
    status: 'PAID',
    subtotal: 23800.0,
    tax: 1666.0,
    totalAmount: 25466.0,
    createdAt: '2024-03-14',
    updatedAt: '2024-03-14',
  },
  {
    id: 'INV-2024-003',
    invoiceNumber: 'INV/2024/02/001',
    customerId: 1,
    simulationId: 'SIM-003',
    invoiceDate: '2024-02-15',
    dueDate: '2024-03-14',
    status: 'PAID',
    subtotal: 27500.0,
    tax: 1925.0,
    totalAmount: 29425.0,
    createdAt: '2024-02-15',
    updatedAt: '2024-02-15',
  },
  {
    id: 'INV-2024-004',
    invoiceNumber: 'INV/2024/02/002',
    customerId: 2,
    simulationId: 'SIM-004',
    invoiceDate: '2024-02-14',
    dueDate: '2024-03-13',
    status: 'PAID',
    subtotal: 22500.0,
    tax: 1575.0,
    totalAmount: 24075.0,
    createdAt: '2024-02-14',
    updatedAt: '2024-02-14',
  },
  {
    id: 'INV-2024-005',
    invoiceNumber: 'INV/2024/01/001',
    customerId: 1,
    simulationId: 'SIM-005',
    invoiceDate: '2024-01-15',
    dueDate: '2024-02-14',
    status: 'PAID',
    subtotal: 25000.0,
    tax: 1750.0,
    totalAmount: 26750.0,
    createdAt: '2024-01-15',
    updatedAt: '2024-01-15',
  },
])

// Monthly invoice data for line chart (sum of both customers)
const monthlyInvoiceData = ref([
  { month: 'Jan', amount: 45000 }, // 25000 + 20000
  { month: 'Feb', amount: 50000 }, // 27500 + 22500
  { month: 'Mar', amount: 48600 }, // 24800 + 23800
  { month: 'Apr', amount: 45000 }, // 23500 + 21500
  { month: 'May', amount: 42000 }, // 22000 + 20000
  { month: 'Jun', amount: 46000 }, // 24000 + 22000
  { month: 'Jul', amount: 49000 }, // 25500 + 23500
  { month: 'Aug', amount: 44000 }, // 23000 + 21000
  { month: 'Sep', amount: 47000 }, // 24500 + 22500
  { month: 'Oct', amount: 44400 }, // 23200 + 21200
  { month: 'Nov', amount: 42000 }, // 22000 + 20000
  { month: 'Dec', amount: 37000 }, // 20000 + 17000
])

const customerMap: Record<number, string> = {
  1: 'Customer A',
  2: 'Customer B',
}

const invoiceColumns = [
  {
    title: 'Invoice Number',
    dataIndex: 'invoiceNumber',
    key: 'invoiceNumber',
  },
  {
    title: 'Customer',
    dataIndex: 'customerId',
    key: 'customerId',
    customRender: ({ text }: { text: number }) => customerMap[text] || `Customer ${text}`,
  },
  {
    title: 'Amount',
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    customRender: ({ text }: { text: number }) => `$${text?.toLocaleString() ?? 0}`,
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: 'Date',
    dataIndex: 'invoiceDate',
    key: 'invoiceDate',
  },
]

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

interface TooltipParam {
  name: string
  value: number
  axisDim: string
  axisIndex: number
  seriesIndex: number
  dataIndex: number
}

// Get chart options for customer monthly data
const getCustomerChartOption = (monthlyData: { month: string; amount: number }[]) => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: TooltipParam[]) => {
      const data = params[0]
      return `${data.name}<br/>${formatCurrency(data.value)}`
    },
  },
  grid: {
    top: '10%',
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: monthlyData.map((item) => item.month),
    boundaryGap: false,
    axisLabel: {
      fontSize: 10,
      interval: 2,
    },
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => formatCurrency(value),
      fontSize: 10,
    },
  },
  series: [
    {
      type: 'line',
      data: monthlyData.map((item) => item.amount),
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2,
        color: '#1890ff',
      },
      itemStyle: {
        color: '#1890ff',
        borderWidth: 2,
        borderColor: '#fff',
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(24,144,255,0.3)',
            },
            {
              offset: 1,
              color: 'rgba(24,144,255,0.1)',
            },
          ],
        },
      },
    },
  ],
})

// Calculate yearly total
const yearlyTotal = computed(() => {
  return monthlyInvoiceData.value.reduce((sum, month) => sum + month.amount, 0)
})

// Chart options for the main monthly trend
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: TooltipParam[]) => {
      const data = params[0]
      return `${data.name}<br/>${formatCurrency(data.value)}`
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: monthlyInvoiceData.value.map((item) => item.month),
    boundaryGap: false,
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => formatCurrency(value),
    },
  },
  series: [
    {
      name: 'Invoice Amount',
      type: 'line',
      data: monthlyInvoiceData.value.map((item) => item.amount),
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      lineStyle: {
        width: 3,
        color: '#1890ff',
      },
      itemStyle: {
        color: '#1890ff',
        borderWidth: 2,
        borderColor: '#fff',
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(24,144,255,0.3)',
            },
            {
              offset: 1,
              color: 'rgba(24,144,255,0.1)',
            },
          ],
        },
      },
    },
  ],
}))

// Add mock data for products with calculation gaps
interface ProductGap {
  id: string
  productCode: string
  productName: string
  oldPrice: number
  newPrice: number
  gapAmount: number
  lastUpdated: string
}

const productGaps = ref<ProductGap[]>([
  {
    id: 'P001',
    productCode: 'SKU-001',
    productName: 'Product Alpha',
    oldPrice: 150.0,
    newPrice: 175.5,
    gapAmount: 25.5,
    lastUpdated: '2024-03-15',
  },
  {
    id: 'P002',
    productCode: 'SKU-002',
    productName: 'Product Beta',
    oldPrice: 220.0,
    newPrice: 198.0,
    gapAmount: -22.0,
    lastUpdated: '2024-03-15',
  },
  {
    id: 'P003',
    productCode: 'SKU-003',
    productName: 'Product Gamma',
    oldPrice: 89.99,
    newPrice: 99.99,
    gapAmount: 10.0,
    lastUpdated: '2024-03-14',
  },
  {
    id: 'P004',
    productCode: 'SKU-004',
    productName: 'Product Delta',
    oldPrice: 445.0,
    newPrice: 425.0,
    gapAmount: -20.0,
    lastUpdated: '2024-03-14',
  },
])

const productGapColumns = [
  {
    title: 'Product Code',
    dataIndex: 'productCode',
    key: 'productCode',
  },
  {
    title: 'Product Name',
    dataIndex: 'productName',
    key: 'productName',
  },
  {
    title: 'Gap Amount',
    dataIndex: 'gapAmount',
    key: 'gapAmount',
    customRender: ({ text }: { text: number }) => {
      const color = text >= 0 ? '#52c41a' : '#f5222d'
      return {
        children: formatCurrency(text),
        props: {
          style: {
            color,
            fontWeight: '600',
          },
        },
      }
    },
  },
  {
    title: 'Last Updated',
    dataIndex: 'lastUpdated',
    key: 'lastUpdated',
  },
]

const handleViewMoreGaps = () => {
  // Navigate to product gaps page
  window.location.href = '/product-gaps'
}

onMounted(async () => {
  try {
    // Using mock data directly
  } catch (error) {
    console.error('Error loading dashboard data:', error)
  }
})
</script>

<template>
  <div class="dashboard-container">
    <h2 class="dashboard-title">Warehouse Operations Dashboard</h2>

    <Row :gutter="[16, 16]">
      <Col :span="12">
        <Card>
          <Statistic
            title="Total Charges (This Year)"
            :value="yearlyTotal"
            :value-style="{ color: '#52c41a', fontSize: '24px' }"
            :precision="2"
            prefix="$"
          >
            <template #prefix>
              <DollarCircleOutlined />
            </template>
          </Statistic>
        </Card>
      </Col>

      <Col :span="12">
        <Card>
          <Statistic
            title="Pending Invoices"
            :value="pendingInvoices"
            :value-style="{ color: '#faad14', fontSize: '24px' }"
          >
            <template #prefix>
              <FileTextOutlined />
            </template>
          </Statistic>
        </Card>
      </Col>
    </Row>

    <Row :gutter="[16, 16]" class="mt-4 chart-row">
      <Col :span="12" class="chart-col">
        <Card title="Stock Charges by Customer" class="chart-card">
          <div class="charges-container">
            <div v-for="item in stockCharges" :key="item.customer" class="charge-item">
              <div class="charge-header">
                <span class="customer-name">{{ item.customer }}</span>
                <span class="total-charge">{{ formatCurrency(item.totalCharge) }}</span>
              </div>
              <div class="customer-chart">
                <v-chart
                  :option="getCustomerChartOption(item.monthlyData)"
                  :autoresize="true"
                  style="height: 120px; width: 100%"
                />
              </div>
            </div>
          </div>
        </Card>
      </Col>

      <Col :span="12" class="chart-col">
        <Card title="Monthly Invoice Trend" class="chart-card">
          <div class="line-chart-container">
            <v-chart class="chart" :option="chartOption" autoresize />
          </div>
        </Card>
      </Col>
    </Row>

    <Row :gutter="[16, 16]" class="mt-4">
      <Col :span="24">
        <Card title="Recent Invoices">
          <Table
            :columns="invoiceColumns"
            :data-source="recentInvoices"
            :pagination="false"
            size="small"
          />
        </Card>
      </Col>
    </Row>

    <Row :gutter="[16, 16]" class="mt-4">
      <Col :span="24">
        <Card>
          <template #title>
            <div class="card-title-with-icon">
              <WarningOutlined style="color: #faad14; margin-right: 8px" />
              Product Price Gaps
            </div>
          </template>
          <div class="gaps-container">
            <Table
              :columns="productGapColumns"
              :data-source="productGaps"
              :pagination="false"
              size="small"
            />
            <div class="view-more-container">
              <Button type="primary" @click="handleViewMoreGaps"> View More </Button>
            </div>
          </div>
        </Card>
      </Col>
    </Row>
  </div>
</template>

<style scoped>
.dashboard-container {
  padding: 24px;
}

.dashboard-title {
  margin-bottom: 24px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
}

.mt-4 {
  margin-top: 24px;
}

.chart-row {
  display: flex;
  margin: 24px -8px;
}

.chart-col {
  display: flex;
  padding: 0 8px;
}

.chart-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart-card :deep(.ant-card-body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.charges-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.charge-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.charge-item:last-child {
  margin-bottom: 0;
}

.charge-item:hover {
  background-color: #f5f5f5;
  border-color: #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.charge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #e8e8e8;
}

.customer-name {
  font-weight: 600;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
}

.total-charge {
  font-weight: 600;
  font-size: 16px;
  color: #52c41a;
}

.customer-chart {
  background-color: #fff;
  border-radius: 4px;
  padding: 8px;
}

.line-chart-container {
  flex: 1;
  min-height: 300px;
  width: 100%;
}

.chart {
  height: 100%;
  width: 100%;
}

/* Remove old chart styles */
.chart-label,
.chart-line,
.chart-point {
  display: none;
}

.gaps-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.view-more-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.card-title-with-icon {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}
</style>
