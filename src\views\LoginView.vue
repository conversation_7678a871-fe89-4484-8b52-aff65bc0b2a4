<script setup lang="ts">
import { reactive, ref, computed, onMounted } from 'vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useAuthService } from '@IVC/services/auth'
import { useAuthStore } from '@IVC/stores/auth'
import { LoginCredentials } from '@IVC/types/login.ts'
import Logo from '@IVC/assets/logo_nittsu.svg'

// Services and stores
const router = useRouter()
const authService = useAuthService()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const defaultCredentials = ref([])
const loginForm = reactive({
  username: '',
  password: '',
  remember: false,
})

// Computed properties
const isLoading = computed(() => authStore.isLoading || loading.value)

onMounted(async () => {
  // Get default credentials for demo
  try {
    defaultCredentials.value = await authService.getDefaultCredentials()
  } catch (error) {
    console.error('Error loading default credentials:', error)
  }
})

// Form validation rules
const rules = {
  username: [
    { required: true, message: 'Please enter your username!', trigger: 'blur' },
    { min: 3, message: 'Username must be at least 3 characters!', trigger: 'blur' },
  ],
  password: [
    { required: true, message: 'Please enter your password!', trigger: 'blur' },
    { min: 3, message: 'Password must be at least 3 characters!', trigger: 'blur' },
  ],
}

// Handle login
const handleLogin = async (values: LoginCredentials) => {
  loading.value = true
  try {
    const result = await authService.login({
      username: values.username,
      password: values.password,
    })

    if (result.success) {
      message.success(result.message || `Welcome back, ${result.user?.username}!`)

      // Show role-based redirect message
      setTimeout(() => {
        if (result.user?.role === 'admin') {
          message.info('Redirecting to Admin Dashboard...')
        } else if (result.user?.role === 'manager') {
          message.info('Redirecting to Manager Dashboard...')
        } else {
          message.info('Redirecting to User Dashboard...')
        }
      }, 1000)

      // Redirect to home page
      setTimeout(() => {
        router.push('/')
      }, 1500)
    } else {
      message.error(result.message || 'Login failed')
    }
  } catch (error) {
    console.error('Login error:', error)
    message.error('Login failed. Please try again.')
  } finally {
    loading.value = false
  }
}

// Handle login failed
const handleLoginFailed = (errorInfo) => {
  console.log('Failed:', errorInfo)
  message.error('Please check your login credentials!')
}
</script>

<template>
  <div class="login-container">
    <div class="login-card-wrapper">
      <!-- Left Side - Welcome Section -->
      <div class="welcome-section">
        <div class="decorative-elements">
          <!-- Decorative Plus Signs -->
          <div class="plus-icon plus-1">+</div>
          <div class="plus-icon plus-2">+</div>

          <!-- Decorative Dots -->
          <div class="dots-pattern">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>

          <!-- Decorative Circles -->
          <div class="circle circle-1"></div>
          <div class="circle circle-2"></div>

          <!-- Flowing Lines -->
          <svg class="flowing-lines" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M20,100 Q60,50 100,100 T180,100"
              stroke="rgba(154,205,50,0.3)"
              stroke-width="2"
              fill="none"
            />
            <path
              d="M30,120 Q70,70 110,120 T190,120"
              stroke="rgba(154,205,50,0.2)"
              stroke-width="1.5"
              fill="none"
            />
            <path
              d="M10,140 Q50,90 90,140 T170,140"
              stroke="rgba(154,205,50,0.25)"
              stroke-width="1.8"
              fill="none"
            />
          </svg>
        </div>

        <!-- Warehouse Illustration -->
        <div class="warehouse-illustration">
          <svg width="280" height="200" viewBox="0 0 280 200" class="warehouse-svg">
            <!-- Background -->
            <rect width="280" height="200" fill="url(#warehouseGradient)" />

            <!-- Ground -->
            <rect x="0" y="160" width="280" height="40" fill="rgba(154,205,50,0.2)" />

            <!-- Main Warehouse Building -->
            <polygon
              points="40,160 40,80 140,60 240,80 240,160"
              fill="rgba(255,255,255,0.15)"
              stroke="rgba(154,205,50,0.4)"
              stroke-width="2"
            />

            <!-- Roof -->
            <polygon points="35,80 140,55 245,80 240,80 140,60 40,80" fill="rgba(154,205,50,0.8)" />

            <!-- Front Wall -->
            <rect x="40" y="80" width="200" height="80" fill="rgba(255,255,255,0.1)" />

            <!-- Large Door -->
            <rect
              x="60"
              y="120"
              width="50"
              height="40"
              fill="rgba(30,58,138,0.3)"
              stroke="rgba(154,205,50,0.6)"
              stroke-width="2"
            />

            <!-- Windows -->
            <rect x="130" y="95" width="20" height="15" fill="rgba(154,205,50,0.6)" />
            <rect x="160" y="95" width="20" height="15" fill="rgba(154,205,50,0.6)" />
            <rect x="190" y="95" width="20" height="15" fill="rgba(154,205,50,0.6)" />

            <!-- Loading Dock -->
            <rect x="130" y="130" width="40" height="30" fill="rgba(30,58,138,0.2)" />

            <!-- Forklift -->
            <g transform="translate(180, 140)">
              <rect x="0" y="10" width="18" height="10" fill="rgba(154,205,50,0.8)" />
              <rect x="14" y="3" width="6" height="17" fill="rgba(154,205,50,0.8)" />
              <circle cx="4" cy="17" r="3" fill="rgba(255,255,255,0.6)" />
              <circle cx="14" cy="17" r="3" fill="rgba(255,255,255,0.6)" />
            </g>

            <!-- Shipping Containers -->
            <rect x="200" y="140" width="30" height="15" fill="rgba(30,58,138,0.3)" />
            <rect x="203" y="143" width="24" height="9" fill="rgba(154,205,50,0.4)" />

            <!-- Gradients -->
            <defs>
              <linearGradient id="warehouseGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" style="stop-color: rgba(30, 58, 138, 0.1); stop-opacity: 1" />
                <stop offset="100%" style="stop-color: rgba(30, 58, 138, 0.05); stop-opacity: 1" />
              </linearGradient>
            </defs>
          </svg>
        </div>

        <div class="welcome-content">
          <h1 class="welcome-title">Warehouse Management System</h1>
          <p class="welcome-subtitle">
            You can sign in to access with your existing warehouse management account.
          </p>
        </div>
      </div>

      <!-- Right Side - Login Form -->
      <div class="form-section">
        <div class="form-content">
          <div class="form-header">
            <div class="form-title-container">
              <img :src="Logo" alt="Logo" class="header-logo" />
            </div>
            <p class="form-subtitle">Sign in to your account</p>
          </div>

          <a-form
            :model="loginForm"
            :rules="rules"
            @finish="handleLogin"
            @finishFailed="handleLoginFailed"
            layout="vertical"
            class="login-form"
          >
            <a-form-item name="username" class="form-item">
              <a-input
                v-model:value="loginForm.username"
                placeholder="Username or email"
                size="large"
                class="custom-input"
              >
                <template #prefix>
                  <UserOutlined class="input-icon" />
                </template>
              </a-input>
            </a-form-item>

            <a-form-item name="password" class="form-item">
              <a-input-password
                v-model:value="loginForm.password"
                placeholder="Password"
                size="large"
                class="custom-input"
              >
                <template #prefix>
                  <LockOutlined class="input-icon" />
                </template>
              </a-input-password>
            </a-form-item>

            <a-form-item class="form-item">
              <div class="form-options">
                <a-checkbox v-model:checked="loginForm.remember" class="remember-checkbox">
                  Remember me
                </a-checkbox>
              </div>
            </a-form-item>

            <a-form-item class="form-item">
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                :loading="isLoading"
                class="login-button"
                block
              >
                Sign In
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Color Variables */
:root {
  --primary-navy: #1e3a8a;
  --primary-lime: #9acd32;
  --white: #ffffff;
  --light-gray: #f8fafc;
  --border-gray: #9acd32;
  --text-gray: #64748b;
  --text-dark: #1e293b;
  --gradient-start: #1e3a8a;
  --gradient-end: #2563eb;
}

.login-container {
  min-height: 100vh;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.login-card-wrapper {
  background: #ffffff;
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  display: flex;
  width: 60vw;
  max-width: 1400px;
  min-width: 1000px;
  height: 700px;
  position: relative;
  margin: 0 auto;
  z-index: 1;
}

/* Left Side - Welcome Section */
.welcome-section {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-navy) 0%, var(--gradient-end) 100%);
  padding: 60px 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
  height: 100%;
}

.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.plus-icon {
  position: absolute;
  color: rgba(154, 205, 50, 0.4);
  font-size: 24px;
  font-weight: 300;
}

.plus-1 {
  top: 80px;
  right: 80px;
}

.plus-2 {
  bottom: 120px;
  left: 60px;
}

.dots-pattern {
  position: absolute;
  top: 60px;
  right: 140px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.dot {
  width: 4px;
  height: 4px;
  background: rgba(154, 205, 50, 0.5);
  border-radius: 50%;
}

.circle {
  position: absolute;
  border: 2px solid rgba(154, 205, 50, 0.3);
  border-radius: 50%;
}

.circle-1 {
  width: 60px;
  height: 60px;
  bottom: 200px;
  right: 40px;
}

.circle-2 {
  width: 40px;
  height: 40px;
  top: 160px;
  left: 40px;
}

.flowing-lines {
  position: absolute;
  bottom: 60px;
  left: 20px;
  width: 180px;
  height: 120px;
  opacity: 0.6;
}

/* Warehouse Illustration */
.warehouse-illustration {
  position: relative;
  z-index: 2;
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
}

.warehouse-svg {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

.welcome-content {
  position: relative;
  z-index: 2;
}

.welcome-title {
  font-size: 42px;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 20px;
  line-height: 1.2;
  text-align: center;
}

.welcome-subtitle {
  font-size: 16px;
  color: #8c8c8c;
  line-height: 1.6;
  margin: 0 0 30px 0;
  text-align: center;
}

/* Warehouse Features */
.warehouse-features {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.feature-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(154, 205, 50, 0.2);
  border: 1px solid rgba(154, 205, 50, 0.3);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.feature-badge:hover {
  background: rgba(154, 205, 50, 0.3);
  transform: translateY(-2px);
}

.feature-badge .feature-icon {
  font-size: 16px;
}

.feature-badge span {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-dark);
}

/* Right Side - Form Section */
.form-section {
  flex: 1;
  background: #ffffff;
  padding: 60px 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  overflow-y: auto;
}

.form-content {
  width: 100%;
  max-width: 320px;
}

.form-header {
  margin-bottom: 40px;
}

.form-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  width: 100%;
}

.header-logo {
  height: 35px;
  width: auto;
  object-fit: contain;
}

.form-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-dark);
  margin: 0;
  line-height: 1;
}

.form-subtitle {
  font-size: 16px;
  color: #8c8c8c;
  margin: 8px 0 0 0;
  text-align: center;
  font-weight: 400;
}

.login-form {
  width: 100%;
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-of-type {
  margin-bottom: 0;
}

.custom-input {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.2s;
  background: #ffffff;
  height: 50px;
  font-weight: 400;
  padding: 4px 11px;
}

.custom-input:hover {
  border-color: #4096ff;
  background: #ffffff;
}

.custom-input:focus,
.custom-input:focus-within {
  border-color: #4096ff;
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
  outline: 0;
}

.input-icon {
  color: var(--text-gray);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.remember-checkbox :deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #90c42d;
  border-color: #90c42d;
}

.remember-checkbox :deep(.ant-checkbox-wrapper) {
  font-size: 14px;
  color: var(--text-gray);
}

.forgot-password {
  color: var(--text-gray);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #1a025b;
}

.login-button {
  background: #1a025b;
  border-color: #1a025b;
  border-radius: 6px;
  height: 50px;
  font-size: 14px;
  font-weight: 400;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  margin-bottom: 30px;
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  box-shadow: 0 2px 0 rgba(26, 2, 91, 0.1);
}

.login-button:hover {
  background: #2a0a6b;
  border-color: #2a0a6b;
  color: #fff;
}

.login-button:active {
  background: #0f0140;
  border-color: #0f0140;
}

.login-button:focus {
  background: #1a025b;
  border-color: #1a025b;
  box-shadow: 0 0 0 2px rgba(26, 2, 91, 0.1);
}

.form-footer {
  text-align: center;
}

.footer-text {
  color: var(--text-gray);
  font-size: 14px;
  margin: 0;
}

.create-account {
  color: #1a025b;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.create-account:hover {
  color: #90c42d;
}

/* Responsive Design - Desktop Only */
@media (max-width: 1600px) {
  .login-card-wrapper {
    width: 75vw;
    max-width: 1200px;
  }
}

@media (max-width: 1400px) {
  .login-card-wrapper {
    width: 80vw;
    max-width: 1100px;
    height: 650px;
  }

  .welcome-title {
    font-size: 42px;
  }

  .form-title {
    font-size: 28px;
  }
}

@media (max-width: 1200px) {
  .login-card-wrapper {
    width: 85vw;
    max-width: 1000px;
    min-width: 900px;
    height: 600px;
  }

  .welcome-section {
    padding: 50px 40px;
  }

  .form-section {
    padding: 50px 40px;
  }

  .welcome-title {
    font-size: 40px;
  }

  .welcome-subtitle {
    font-size: 16px;
    max-width: 300px;
  }

  .form-title {
    font-size: 26px;
  }

  .warehouse-svg {
    width: 240px;
    height: 170px;
  }
}
</style>
