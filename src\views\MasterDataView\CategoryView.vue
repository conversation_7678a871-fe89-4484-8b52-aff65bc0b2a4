<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSQLite } from '@IVC/hooks/useSQLite'
import WorkoutForm from '@IVC/components/dialogs/WorkoutForm.vue'
import CategoryForm from '@IVC/components/dialogs/CategoryForm.vue' // Import CategoryForm
import { Modal, message, Tag, Button, PageHeader } from 'ant-design-vue' // Removed Space, Table. Using a-table directly.
import { PlusOutlined } from '@ant-design/icons-vue'
import ActionButtons from '@IVC/components/ActionButtons.vue' // Assuming this component exists and is flexible

import type { EntityStatus } from '@IVC/types/common'

import {
  getAllCategories,
  addCategory,
  updateCategory,
  deleteCategory,
  getWorkoutsByCategoryId,
  addWorkout,
  updateWorkout,
  deleteWorkout,
} from '@IVC/services/master-data/category' // Adjust path if services are split
import type { Category, Workout } from '@IVC/types/MasterDataTypes/Category'
import { getDebitCodes } from '@IVC/services/master-data/companyProfile/companyProfileService'
import type { DebitCode } from '@IVC/types/CompanyProfile'

const { isLoading: isSQLiteLoading } = useSQLite()
const localLoading = ref(false) // For general loading state within the component

const categories = ref<Category[]>([])
const debitCodes = ref<DebitCode[]>([]) // For debit code lookup in workout table
const isCategoryModalVisible = ref(false)
const editingCategory = ref<Partial<Category> | null>(null) // Use Partial for new category form
const categoryFormDialogRef = ref<InstanceType<typeof CategoryForm> | null>(null) // Ref for CategoryForm component

// State for WorkoutForm modal
const isWorkoutModalVisible = ref(false)
const workoutFormRef = ref<InstanceType<typeof WorkoutForm> | null>(null)
const currentCategoryForWorkoutContext = ref<Category | null>(null)
const editingWorkoutDataForForm = ref<Partial<Workout> | null>(null) // Data to pass to WorkoutForm

const categoryColumns = [
  {
    title: 'Code',
    dataIndex: 'code',
    key: 'code',
    sorter: (a: Category, b: Category) => a.code.localeCompare(b.code),
  },
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    sorter: (a: Category, b: Category) => a.name.localeCompare(b.name),
  },
  { title: 'Status', dataIndex: 'status', key: 'c_status', width: 120 }, // Added status column for Category
  { title: 'Action', key: 'action', width: 150 },
]

const workoutColumns = [
  {
    title: 'Workout Code',
    dataIndex: 'code',
    key: 'w_code',
    sorter: (a: Workout, b: Workout) => a.code.localeCompare(b.code),
  },
  {
    title: 'Workout Name',
    dataIndex: 'name',
    key: 'w_name',
    sorter: (a: Workout, b: Workout) => a.name.localeCompare(b.name),
  },
  {
    title: 'Unit',
    dataIndex: 'unitName', // Assuming Workout type has unitName and unitCode
    key: 'w_unit',
    customRender: ({ record }: { record: Workout }) => {
      if (!record.unitName) return 'N/A'
      // Determine the sign: use '-' if unitPriceSign is '-' or if unitPrice is negative (for backward compatibility)
      // Otherwise, the sign is effectively positive but won't be displayed.
      return `${record.unitName}`
    },
  },
  {
    title: 'Unit Price',
    dataIndex: 'unitPrice',
    key: 'w_unitPrice',
    customRender: ({ record }: { record: Workout }) => {
      console.log(record)
      if (record.unitPrice == null) return 'N/A'

      const absolutePrice = Math.abs(record.unitPrice)
      const unit = record.unitPriceIsPercentage ? '%' : 'VNĐ' // Assuming VND if not PERCENT

      return `${record.unitCode === '-' ? '-' : ''}${absolutePrice.toLocaleString()}${unit}`
    },
  },
  {
    title: 'VAT',
    dataIndex: 'vat',
    key: 'w_vat',
    customRender: ({ value }: { value?: number }) =>
      value != null ? `${(value * 100).toFixed(0)}%` : 'N/A',
  },
  {
    title: 'Calc. Type',
    dataIndex: 'calculationType',
    key: 'w_calc_type',
    customRender: ({ record }: { record: Workout }) => {
      if (record.calculationType === 'Link') {
        return `Link (to: ${record.linkedWorkoutName || record.linkedWorkoutId || 'N/A'})`
      }
      return record.calculationType || 'N/A'
    },
  },
  {
    title: 'Debit Code',
    dataIndex: 'debitCodeId',
    key: 'w_debit_code',
    customRender: ({ record }: { record: Workout }) => {
      if (!record.debitCodeId) return 'N/A'
      const debitCode = debitCodes.value.find((code) => code.id === record.debitCodeId)
      return debitCode ? `${debitCode.name} (${debitCode.code})` : 'N/A'
    },
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'w_status',
  },
  { title: 'Workout Action', key: 'w_action', width: 180 },
]

const fetchCategories = async () => {
  localLoading.value = true
  try {
    categories.value = await getAllCategories()
  } catch (err) {
    message.error(`Failed to load categories: ${(err as Error).message}`)
    console.error(err)
  } finally {
    localLoading.value = false
  }
}

const fetchDebitCodes = async () => {
  try {
    debitCodes.value = await getDebitCodes()
  } catch (err) {
    console.error('Failed to load debit codes:', err)
    // Don't show error message as this is not critical for the main functionality
  }
}

onMounted(async () => {
  await Promise.all([fetchCategories(), fetchDebitCodes()])
})

// Category Modal and Form Logic
// categoryModalTitle will be handled by CategoryForm.vue or can be passed as a prop if needed

const showAddCategoryModal = () => {
  editingCategory.value = { code: '', name: '' } // Clear for new entry
  isCategoryModalVisible.value = true
}

const showEditCategoryModal = (category: Category) => {
  editingCategory.value = { ...category }
  isCategoryModalVisible.value = true
}

const handleCategoryModalClose = () => {
  isCategoryModalVisible.value = false
  editingCategory.value = null // Also resets categoryFormState via watch
  categoryFormDialogRef.value?.resetFormAndLoading()
}

const handleCategorySave = async (categoryData: Category | Omit<Category, 'id'>) => {
  // This function will be called by the @save event from CategoryForm.vue
  try {
    localLoading.value = true
    if ('id' in categoryData && categoryData.id) {
      await updateCategory(categoryData as Category)
      message.success('Category updated successfully!')
    } else {
      await addCategory(categoryData as Omit<Category, 'id'>)
      message.success('Category added successfully!')
    }
    await fetchCategories()
    handleCategoryModalClose()
  } catch (err) {
    message.error(`Failed to save category: ${(err as Error).message}`)
    console.error(err)
  } finally {
    localLoading.value = false
  }
}

const confirmDeleteCategory = (id: number, name: string) => {
  Modal.confirm({
    title: 'Are you sure you want to delete this category?',
    content: `This action will permanently delete the category "${name}". Associated workouts might be affected.`,
    okText: 'Delete',
    okType: 'danger',
    cancelText: 'Cancel',
    async onOk() {
      try {
        localLoading.value = true
        await deleteCategory(id)
        message.success('Category deleted successfully!')
        await fetchCategories()
      } catch (err) {
        message.error(`Failed to delete category: ${(err as Error).message}`)
        console.error(err)
      } finally {
        localLoading.value = false
      }
    },
  })
}

// Method to handle category status update from ActionButtons
const handleSetCategoryStatus = async (category: Category, newStatus: EntityStatus) => {
  try {
    localLoading.value = true
    await updateCategory({ ...category, status: newStatus })
    message.success(`Category "${category.name}" status updated to ${newStatus}.`)
    await fetchCategories() // Refresh the list
  } catch (err) {
    message.error(
      `Failed to update status for category "${category.name}": ${(err as Error).message}`,
    )
    console.error(err)
  } finally {
    localLoading.value = false
  }
}

// Refresh workouts for a specific category (typically after an add/edit/delete operation on a workout)
const refreshWorkoutsForCategory = async (categoryId: number) => {
  try {
    const fetchedWorkouts = await getWorkoutsByCategoryId(categoryId)
    const categoryIndex = categories.value.findIndex((c) => c.id === categoryId)
    if (categoryIndex !== -1) {
      // Create a new object for the category to ensure reactivity when updating its workouts array
      categories.value[categoryIndex] = {
        ...categories.value[categoryIndex],
        workouts: fetchedWorkouts,
      }
    }
  } catch (err) {
    message.error(
      `Failed to refresh workouts for category ID ${categoryId}: ${(err as Error).message}`,
    )
  }
}

// Workout Modal Logic (for adding workout to a category or editing existing workout)
const openAddWorkoutToCategoryModal = (category: Category) => {
  currentCategoryForWorkoutContext.value = category
  // Prepare minimal data for WorkoutForm, including categoryId.
  editingWorkoutDataForForm.value = { categoryId: category.id } as Partial<Workout>
  isWorkoutModalVisible.value = true
}

const openEditWorkoutModal = (workout: Workout) => {
  editingWorkoutDataForForm.value = { ...workout } // Pass full workout data for editing
  isWorkoutModalVisible.value = true
}

const handleWorkoutModalClose = () => {
  isWorkoutModalVisible.value = false
  currentCategoryForWorkoutContext.value = null
  editingWorkoutDataForForm.value = null
  workoutFormRef.value?.resetFormAndLoading()
}

const handleWorkoutModalSave = async (workoutDataFromForm: Partial<Workout>) => {
  localLoading.value = true
  try {
    let categoryIdToRefresh: number | undefined

    if (workoutDataFromForm.id) {
      // Editing existing workout
      await updateWorkout(workoutDataFromForm as Workout)
      message.success('Workout updated successfully!')
      categoryIdToRefresh = (editingWorkoutDataForForm.value as Workout)?.categoryId
    } else {
      // Adding new workout
      if (!currentCategoryForWorkoutContext.value?.id) {
        message.error('Category context is missing for new workout. Cannot save.')
        localLoading.value = false
        return
      }
      const finalWorkoutData = {
        ...workoutDataFromForm,
        categoryId: currentCategoryForWorkoutContext.value.id,
      } as Omit<Workout, 'id' | 'unitCode' | 'unitName' | 'categoryName'> // Adjust type for addWorkout

      await addWorkout(finalWorkoutData) // Ensure type compatibility with your service
      message.success(
        `Workout added to category "${currentCategoryForWorkoutContext.value.name}" successfully!`,
      )
      categoryIdToRefresh = currentCategoryForWorkoutContext.value.id
    }

    if (categoryIdToRefresh) {
      await refreshWorkoutsForCategory(categoryIdToRefresh)
    }
    handleWorkoutModalClose()
  } catch (err) {
    message.error(`Failed to add workout: ${(err as Error).message}`)
    console.error(err)
  } finally {
    localLoading.value = false
    workoutFormRef.value?.resetFormAndLoading()
  }
}

const confirmDeleteWorkoutFromSubTable = (workout: Workout) => {
  Modal.confirm({
    title: 'Are you sure you want to delete this workout?',
    content: `This action will permanently delete the workout "${workout.name}".`,
    okText: 'Delete',
    okType: 'danger',
    cancelText: 'Cancel',
    async onOk() {
      try {
        localLoading.value = true
        await deleteWorkout(workout.id)
        message.success('Workout deleted successfully!')
        if (workout.categoryId) {
          await refreshWorkoutsForCategory(workout.categoryId)
        }
      } catch (err) {
        message.error(`Failed to delete workout: ${(err as Error).message}`)
        console.error(err)
      } finally {
        localLoading.value = false
      }
    },
  })
}

const handleSetWorkoutStatus = async (workout: Workout, newStatus: EntityStatus) => {
  try {
    localLoading.value = true
    await updateWorkout({ ...workout, status: newStatus })
    message.success(`Workout "${workout.name}" status updated to ${newStatus}.`)
    if (workout.categoryId) {
      await refreshWorkoutsForCategory(workout.categoryId)
    }
  } catch (err) {
    message.error(
      `Failed to update status for workout "${workout.name}": ${(err as Error).message}`,
    )
    console.error(err)
  } finally {
    localLoading.value = false
  }
}

const handleCategoryExpand = async (expanded: boolean, record: Category) => {
  if (expanded && record.id && (!record.workouts || record.workouts.length === 0)) {
    // Fetch workouts only if expanded and not already fetched for this session/expansion
    await refreshWorkoutsForCategory(record.id)
  }
}
</script>

<template>
  <div>
    <PageHeader title="Category Management" sub-title="Manage categories and add workouts to them">
      <template #extra>
        <Button type="primary" @click="showAddCategoryModal">
          <PlusOutlined /> Add Category
        </Button>
      </template>
    </PageHeader>

    <a-table
      :columns="categoryColumns"
      :data-source="categories"
      :loading="localLoading || isSQLiteLoading"
      row-key="id"
      @expand="handleCategoryExpand"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <ActionButtons
            name="Category"
            :record-status="record.status"
            :show-edit="true"
            :show-delete="true"
            :show-add-workout="true"
            :show-set-status="true"
            :show-save="false"
            @edit="showEditCategoryModal(record)"
            @delete="confirmDeleteCategory(record.id, record.name)"
            @add-workout="openAddWorkoutToCategoryModal(record)"
            @set-status="(newStatus: EntityStatus) => handleSetCategoryStatus(record, newStatus)"
          />
        </template>
        <template v-else-if="column.key === 'c_status' && record.status">
          <Tag :color="record.status === 'active' ? 'green' : 'red'">
            {{ record.status.toUpperCase() }}
          </Tag>
        </template>
      </template>
      <template #expandedRowRender="{ record: categoryRecord }">
        <div v-if="categoryRecord.id" style="padding: 10px 24px; background-color: #f9f9f9">
          <a-table
            :columns="workoutColumns"
            :data-source="categoryRecord.workouts"
            :loading="localLoading && currentCategoryForWorkoutContext?.id === categoryRecord.id"
            row-key="id"
            size="small"
            :pagination="{ pageSize: 5, hideOnSinglePage: true }"
          >
            <template #bodyCell="{ column: workoutColumn, record: workoutRecord }">
              <template v-if="workoutColumn.key === 'w_action'">
                <ActionButtons
                  name="Workout"
                  :record-status="workoutRecord.status"
                  :show-edit="true"
                  :show-delete="true"
                  :show-set-status="true"
                  :show-save="false"
                  @edit="openEditWorkoutModal(workoutRecord)"
                  @delete="confirmDeleteWorkoutFromSubTable(workoutRecord)"
                  @set-status="
                    (newStatus: EntityStatus) => handleSetWorkoutStatus(workoutRecord, newStatus)
                  "
                />
              </template>
              <template v-else-if="workoutColumn.key === 'w_status'">
                <Tag :color="workoutRecord.status === 'active' ? 'green' : 'red'">
                  {{ workoutRecord.status?.toUpperCase() }}
                </Tag>
              </template>
            </template>
          </a-table>
          <div
            v-if="!categoryRecord.workouts || categoryRecord.workouts.length === 0"
            style="text-align: center; color: #888; padding: 10px 0"
          >
            No workouts added to this category yet.
          </div>
        </div>
      </template>
    </a-table>

    <!-- Category Add/Edit Modal -->
    <!-- Use CategoryForm.vue component for the modal -->
    <CategoryForm
      ref="categoryFormDialogRef"
      :visible="isCategoryModalVisible"
      :category-data="editingCategory"
      @cancel="handleCategoryModalClose"
      @save="handleCategorySave"
    />

    <!-- Workout Add Modal (using existing WorkoutForm component) -->
    <WorkoutForm
      ref="workoutFormRef"
      :visible="isWorkoutModalVisible"
      :workout-data="editingWorkoutDataForForm"
      @close="handleWorkoutModalClose"
      @save="handleWorkoutModalSave"
    />
  </div>
</template>

<style scoped>
/* Add any specific styles for CategoryView if needed e.g., for expanded row */
</style>
