import { useSQLite } from '@IVC/hooks/useSQLite'
import type { ImportBatch, ImportedProduct } from '@IVC/types/MasterDataTypes/Product'

const { executeQuery, tables } = useSQLite()

export async function createImportBatch(
  batchData: Omit<ImportBatch, 'id' | 'importedAt'>,
): Promise<ImportBatch> {
  const { sourceFileName, importedByUserId, notes } = batchData
  const query = `
    INSERT INTO ${tables.import_batches} (source_file_name, imported_by_user_id, notes)
    VALUES (?, ?, ?);
  `
  const params = [sourceFileName, importedByUserId ?? null, notes ?? null]
  await executeQuery(query, params)
  const idResult = await executeQuery('SELECT last_insert_rowid() as id')
  const firstRow = idResult.result.resultRows?.[0]
  if (firstRow && typeof firstRow[0] === 'number') {
    const newBatchId = firstRow[0]
    // Fetch the newly created batch to get all its details including timestamp
    const newBatchQuery = `SELECT id, imported_at, imported_by_user_id, source_file_name, notes FROM ${tables.import_batches} WHERE id = ?`
    const newBatchResult = await executeQuery(newBatchQuery, [newBatchId])
    const newBatchRow = newBatchResult.result.resultRows?.[0]
    if (newBatchRow) {
      return {
        id: newBatchRow[0] as number,
        importedAt: newBatchRow[1] as string,
        importedByUserId: newBatchRow[2] as number | undefined,
        sourceFileName: newBatchRow[3] as string | undefined,
        notes: newBatchRow[4] as string | undefined,
      }
    }
  }
  throw new Error('Failed to retrieve ID or details after import batch insertion.')
}

export async function findImportedProductByCodeAndCustomer(
  productCode: string,
  customerId: number,
): Promise<ImportedProduct | null> {
  const query = `
    SELECT id, product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, imported_at, import_batch_id
    FROM ${tables.imported_products}
    WHERE product_code = ? AND customer_id = ?
    ORDER BY import_batch_id DESC, id DESC LIMIT 1; -- Get the latest one if multiple (should not happen with unique constraint on product_code, customer_id if no batch consideration)
  `
  const result = await executeQuery(query, [productCode, customerId])
  const row = result.result.resultRows?.[0]
  if (row) {
    return {
      id: row[0] as number,
      productCode: row[1] as string,
      productName: row[2] as string | undefined,
      productPpcNum: row[3] as string | undefined,
      productHrc1: row[4] as string | undefined,
      productHrc3: row[5] as string | undefined,
      productPcm3: row[6] as string | undefined,
      customerId: row[7] as number, // customerId will be present due to WHERE clause
      importedAt: row[8] as string,
      importBatchId: row[9] as number,
    }
  }
  return null
}

export async function addImportedProduct(
  productData: Omit<ImportedProduct, 'id' | 'importedAt'>,
): Promise<ImportedProduct> {
  const {
    productCode,
    productName,
    productPpcNum,
    productHrc1,
    productHrc3,
    productPcm3,
    customerId,
    importBatchId,
  } = productData

  const query = `
    INSERT INTO ${tables.imported_products}
      (product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, import_batch_id)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?);
  `
  const params = [
    productCode,
    productName ?? null,
    productPpcNum ?? null,
    productHrc1 ?? null,
    productHrc3 ?? null,
    productPcm3 ?? null,
    customerId ?? null,
    importBatchId,
  ]
  await executeQuery(query, params)
  const idResult = await executeQuery('SELECT last_insert_rowid() as id')
  const firstRow = idResult.result.resultRows?.[0]
  if (firstRow && typeof firstRow[0] === 'number') {
    const newProductId = firstRow[0]
    // Fetch the newly created product to get all its details including timestamp
    const newProductQuery = `SELECT id, product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, imported_at, import_batch_id FROM ${tables.imported_products} WHERE id = ?`
    const newProductResult = await executeQuery(newProductQuery, [newProductId])
    const newProductRow = newProductResult.result.resultRows?.[0]
    if (newProductRow) {
      return {
        id: newProductRow[0] as number,
        productCode: newProductRow[1] as string,
        productName: newProductRow[2] as string | undefined,
        productPpcNum: newProductRow[3] as string | undefined,
        productHrc1: newProductRow[4] as string | undefined,
        productHrc3: newProductRow[5] as string | undefined,
        productPcm3: newProductRow[6] as string | undefined,
        customerId: newProductRow[7] as number | undefined,
        importedAt: newProductRow[8] as string,
        importBatchId: newProductRow[9] as number,
      }
    }
  }
  throw new Error('Failed to retrieve ID or details after imported product insertion.')
}

export async function upsertImportedProduct(
  productData: Omit<ImportedProduct, 'id' | 'importedAt'>,
): Promise<{ product: ImportedProduct; operation: 'inserted' | 'updated' }> {
  const {
    productCode,
    productName,
    productPpcNum,
    productHrc1,
    productHrc3,
    productPcm3,
    customerId,
    importBatchId,
  } = productData

  if (!customerId) {
    throw new Error('Customer ID is required to upsert a product.')
  }

  const existingProduct = await findImportedProductByCodeAndCustomer(productCode, customerId)

  if (existingProduct) {
    // UPDATE existing product
    // We update all fields from the new import, including the new importBatchId.
    // The original 'importedAt' of the record is preserved.
    const fieldsToUpdate: Partial<Omit<ImportedProduct, 'id' | 'importedAt' | 'customerId'>> = {
      productName: productName ?? undefined,
      productPpcNum: productPpcNum ?? undefined,
      productHrc1: productHrc1 ?? undefined,
      productHrc3: productHrc3 ?? undefined,
      productPcm3: productPcm3 ?? undefined,
      importBatchId: importBatchId, // Link to the new batch
    }

    const fieldEntries = Object.entries(fieldsToUpdate).filter(([, value]) => value !== undefined)
    if (fieldEntries.length === 0) {
      // No actual fields to update other than potentially batchId, but we'll consider it updated.
      return { product: { ...existingProduct, importBatchId }, operation: 'updated' }
    }

    const setClause = fieldEntries
      .map(([key]) => `${key.replace(/([A-Z])/g, '_$1').toLowerCase()} = ?`)
      .join(', ')
    const values = fieldEntries.map(([, value]) => value)

    const query = `UPDATE ${tables.imported_products} SET ${setClause} WHERE id = ?;`
    await executeQuery(query, [...values, existingProduct.id])
    const updatedProduct = { ...existingProduct, ...fieldsToUpdate } as ImportedProduct
    return { product: updatedProduct, operation: 'updated' }
  } else {
    // INSERT new product
    const newProduct = await addImportedProduct(productData)
    return { product: newProduct, operation: 'inserted' }
  }
}

export async function getImportedProductsByBatch(batchId: number): Promise<ImportedProduct[]> {
  const query = `
    SELECT id, product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, imported_at, import_batch_id
    FROM ${tables.imported_products}
    WHERE import_batch_id = ? ORDER BY product_code ASC;
  `
  const result = await executeQuery(query, [batchId])
  if (!result.result.resultRows) return []
  return result.result.resultRows.map((row: unknown[]) => ({
    id: row[0] as number,
    productCode: row[1] as string,
    productName: row[2] as string | undefined,
    productPpcNum: row[3] as string | undefined,
    productHrc1: row[4] as string | undefined,
    productHrc3: row[5] as string | undefined,
    productPcm3: row[6] as string | undefined,
    customerId: row[7] as number | undefined,
    importedAt: row[8] as string,
    importBatchId: row[9] as number,
  }))
}

export async function getLatestImportBatchForProduct(
  productCode: string,
  customerId?: number,
  excludeBatchId?: number, // To find the one *before* the current one
): Promise<ImportBatch | null> {
  let query = `
    SELECT ib.id, ib.imported_at, ib.imported_by_user_id, ib.source_file_name, ib.notes
    FROM ${tables.import_batches} ib
    JOIN ${tables.imported_products} ip ON ip.import_batch_id = ib.id
    WHERE ip.product_code = ?
  `
  const params: (string | number | undefined)[] = [productCode]

  if (customerId !== undefined) {
    query += ` AND ip.customer_id = ?`
    params.push(customerId)
  } else {
    query += ` AND ip.customer_id IS NULL`
  }
  if (excludeBatchId !== undefined) {
    query += ` AND ib.id < ?` // Assuming batch IDs are incremental
    params.push(excludeBatchId)
  }
  query += ` ORDER BY ib.id DESC LIMIT 1;` // Get the latest one matching criteria

  const result = await executeQuery(
    query,
    params.filter((p) => p !== undefined),
  )
  const row = result.result.resultRows?.[0]
  if (row) {
    return {
      id: row[0] as number,
      importedAt: row[1] as string,
      importedByUserId: row[2] as number | undefined,
      sourceFileName: row[3] as string | undefined,
      notes: row[4] as string | undefined,
    }
  }
  return null
}

export async function getProductsFromBatch(batchId: number): Promise<ImportedProduct[]> {
  // This is the same as getImportedProductsByBatch, can be reused or kept separate for clarity
  return getImportedProductsByBatch(batchId)
}

export async function deleteImportedProduct(productId: number): Promise<void> {
  const query = `DELETE FROM ${tables.imported_products} WHERE id = ?;`
  await executeQuery(query, [productId])
}

export async function updateImportedProduct(
  product: Partial<ImportedProduct> & { id: number },
): Promise<void> {
  const { id, ...fieldsToUpdate } = product
  const fieldEntries = Object.entries(fieldsToUpdate).filter(([, value]) => value !== undefined)

  if (fieldEntries.length === 0) return

  const setClause = fieldEntries
    .map(([key]) => `${key.replace(/([A-Z])/g, '_$1').toLowerCase()} = ?`)
    .join(', ')
  const values = fieldEntries.map(([, value]) => value)

  const query = `UPDATE ${tables.imported_products} SET ${setClause} WHERE id = ?;`
  await executeQuery(query, [...values, id])
}

export async function getAllImportedProducts(): Promise<ImportedProduct[]> {
  const { executeQuery, tables } = useSQLite()
  // Explicitly list columns to ensure order and fetch only what's needed, matching the ImportedProduct type.
  // This makes the mapping more robust than SELECT *.
  const query = `
    SELECT id, product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, imported_at, import_batch_id
    FROM ${tables.imported_products}
    ORDER BY customer_id, import_batch_id DESC, imported_at DESC;
  `
  const result = await executeQuery(query)

  // Use resultRows and map them, consistent with getImportedProductsByBatch
  if (!result.result.resultRows) return []
  return result.result.resultRows.map((row: unknown[]) => ({
    id: row[0] as number,
    productCode: row[1] as string,
    productName: row[2] as string | undefined,
    productPpcNum: row[3] as string | undefined,
    productHrc1: row[4] as string | undefined,
    productHrc3: row[5] as string | undefined,
    productPcm3: row[6] as string | undefined,
    customerId: row[7] as number | undefined,
    importedAt: row[8] as string,
    importBatchId: row[9] as number,
  }))
}

export async function getAllImportedProductsByCustomer(
  customerId: number,
): Promise<ImportedProduct[]> {
  const { executeQuery, tables } = useSQLite()
  const query = `
    SELECT id, product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, imported_at, import_batch_id
    FROM ${tables.imported_products}
    WHERE customer_id = ?
    ORDER BY import_batch_id DESC, imported_at DESC;
  `
  const result = await executeQuery(query, [customerId])

  if (!result.result.resultRows) return []
  return result.result.resultRows.map((row: unknown[]) => ({
    id: row[0] as number,
    productCode: row[1] as string,
    productName: row[2] as string | undefined,
    productPpcNum: row[3] as string | undefined,
    productHrc1: row[4] as string | undefined,
    productHrc3: row[5] as string | undefined,
    productPcm3: row[6] as string | undefined,
    customerId: row[7] as number | undefined,
    importedAt: row[8] as string,
    importBatchId: row[9] as number,
  }))
}
