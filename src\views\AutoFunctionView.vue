<template>
  <div class="auto-focus-management">
    <div class="header">
      <h1>Auto Function Management</h1>
      <a-button type="primary" @click="showCreateModal">
        <template #icon>
          <PlusOutlined />
        </template>
        Add Auto Function
      </a-button>
    </div>

    <!-- Auto Function Table -->
    <a-table
      :columns="columns"
      :data-source="filteredAutoFocusItems"
      :loading="loading"
      :pagination="pagination"
      row-key="id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'customerName'">
          <div class="customer-tags">
            <a-tag
              v-if="record.customerIds.length === customers.length"
              color="green"
              class="all-customers-tag"
            >
              All
            </a-tag>
            <template v-else>
              <a-tag
                v-for="(customerName, index) in record.customerNames"
                :key="index"
                color="blue"
              >
                {{ customerName }}
              </a-tag>
            </template>
          </div>
        </template>
        <template v-else-if="column.key === 'note'">
          <div class="note-cell">
            {{ record.note || '-' }}
          </div>
        </template>
        <template v-else-if="column.key === 'actions'">
          <ActionButtons
            name="Auto Focus"
            :show-save="false"
            :show-edit="true"
            :show-delete="true"
            :show-view="false"
            @edit="editAutoFocus(record)"
            @delete="deleteAutoFocus(record)"
          />
        </template>
      </template>
    </a-table>

    <!-- Create/Edit Modal -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEditing ? 'Edit Auto Function' : 'Create Auto Function'"
      :confirm-loading="saving"
      :ok-text="isEditing ? 'Update' : 'Create'"
      cancel-text="Cancel"
      @ok="handleSave"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="Code" name="code" required>
          <a-input
            v-model:value="formData.code"
            placeholder="Enter auto focus code"
            :disabled="isEditing"
          />
        </a-form-item>

        <a-form-item label="Name" name="name" required>
          <a-input v-model:value="formData.name" placeholder="Enter auto focus name" />
        </a-form-item>

        <a-form-item label="Customers" name="customerIds" required>
          <div class="customer-select-container">
            <a-checkbox
              :checked="isAllCustomersSelected"
              :indeterminate="isSomeCustomersSelected"
              @change="handleSelectAllCustomers"
              class="select-all-checkbox"
            >
              Select All Customers
            </a-checkbox>
            <a-select
              v-model:value="formData.customerIds"
              mode="multiple"
              placeholder="Select customers"
              show-search
              :filter-option="filterCustomerOption"
              :max-tag-count="2"
              :max-tag-text-length="10"
              class="customer-select"
            >
              <a-select-option
                v-for="customer in customers"
                :key="customer.id"
                :value="customer.id"
              >
                {{ customer.name }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-item>

        <a-form-item label="Note" name="note">
          <a-textarea
            v-model:value="formData.note"
            placeholder="Enter note (optional)"
            :rows="4"
            show-count
            :maxlength="500"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { TableColumnsType, FormInstance } from 'ant-design-vue'
import type { AutoFunction, AutoFunctionFormData } from '../types/AutoFunction'
import type { Customer } from '../types/MasterDataTypes/Customer'
import {
  getAutoFocusItems,
  createAutoFocus,
  updateAutoFocus,
  deleteAutoFocus as deleteAutoFocusService,
} from '../services/master-data/autoFunction/autoFunctionService'
import { getAllCustomers } from '../services/master-data/customer/index'
import ActionButtons from '../components/ActionButtons.vue'

// Reactive data
const loading = ref(false)
const saving = ref(false)
const deleting = ref(false)
const modalVisible = ref(false)
const isEditing = ref(false)
const editingId = ref<number | null>(null)
const formRef = ref<FormInstance>()

// Data
const autoFocusItems = ref<AutoFunction[]>([])
const customers = ref<Customer[]>([])

// Search and filters
const searchText = ref('')
const selectedCustomerId = ref<number | undefined>()

// Form data
const formData = reactive<AutoFunctionFormData>({
  code: '',
  name: '',
  customerIds: [],
  note: '',
})

// Table columns
const columns: TableColumnsType = [
  {
    title: 'Code',
    dataIndex: 'code',
    key: 'code',
    width: 250,
    sorter: (a: AutoFunction, b: AutoFunction) => a.code.localeCompare(b.code),
  },
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    width: 450,
    sorter: (a: AutoFunction, b: AutoFunction) => a.name.localeCompare(b.name),
  },
  {
    title: 'Customer',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 250,
    sorter: (a: AutoFunction, b: AutoFunction) => {
      const aCustomer = a.customerNames?.join(', ') || ''
      const bCustomer = b.customerNames?.join(', ') || ''
      return aCustomer.localeCompare(bCustomer)
    },
  },
  {
    title: 'Note',
    dataIndex: 'note',
    key: 'note',
    ellipsis: true,
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 100,
    align: 'center',
  },
]

// Pagination
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `${range[0]}-${range[1]} of ${total} items`,
})

// Form validation rules
const rules = {
  code: [
    { required: true, message: 'Please enter code' },
    { min: 2, max: 20, message: 'Code must be 2-20 characters' },
  ],
  name: [
    { required: true, message: 'Please enter name' },
    { min: 2, max: 100, message: 'Name must be 2-100 characters' },
  ],
  customerIds: [{ required: true, message: 'Please select at least one customer' }],
}

// Computed
const isAllCustomersSelected = computed(() => {
  return customers.value.length > 0 && formData.customerIds.length === customers.value.length
})

const isSomeCustomersSelected = computed(() => {
  return formData.customerIds.length > 0 && formData.customerIds.length < customers.value.length
})

const filteredAutoFocusItems = computed(() => {
  let items = [...autoFocusItems.value]

  // Apply search filter
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    items = items.filter(
      (item) =>
        item.code.toLowerCase().includes(search) ||
        item.name.toLowerCase().includes(search) ||
        item.note?.toLowerCase().includes(search) ||
        item.customerNames?.some((name) => name.toLowerCase().includes(search)),
    )
  }

  // Apply customer filter
  if (selectedCustomerId.value) {
    items = items.filter((item) => item.customerIds.includes(selectedCustomerId.value!))
  }

  // Apply pagination
  const start = (pagination.current - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return items.slice(start, end)
})

// Watch for changes and update pagination total
import { watch } from 'vue'
watch(
  () => {
    let items = [...autoFocusItems.value]
    if (searchText.value) {
      const search = searchText.value.toLowerCase()
      items = items.filter(
        (item) =>
          item.code.toLowerCase().includes(search) ||
          item.name.toLowerCase().includes(search) ||
          item.note?.toLowerCase().includes(search) ||
          item.customerNames?.some((name) => name.toLowerCase().includes(search)),
      )
    }
    if (selectedCustomerId.value) {
      items = items.filter((item) => item.customerIds.includes(selectedCustomerId.value!))
    }
    return items.length
  },
  (newTotal) => {
    pagination.total = newTotal
  },
  { immediate: true },
)

// Methods
const loadAutoFocusItems = async (showLoading = true) => {
  try {
    if (showLoading) {
      loading.value = true
    }
    autoFocusItems.value = await getAutoFocusItems()
  } catch (error) {
    message.error('Failed to load auto focus items')
    console.error('Error loading auto focus items:', error)
  } finally {
    if (showLoading) {
      loading.value = false
    }
  }
}

const loadCustomers = async () => {
  try {
    console.log('Loading customers from database...')
    customers.value = await getAllCustomers()
    console.log('Customers loaded:', customers.value.length, customers.value)

    if (customers.value.length === 0) {
      console.warn('No customers found! Database may not be initialized properly.')
      message.warning('No customers found. Please check database initialization.')
    }
  } catch (error) {
    message.error('Failed to load customers')
    console.error('Error loading customers:', error)
  }
}

const showCreateModal = () => {
  isEditing.value = false
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

const editAutoFocus = (record: AutoFunction) => {
  isEditing.value = true
  editingId.value = record.id
  formData.code = record.code
  formData.name = record.name
  formData.customerIds = [...record.customerIds]
  formData.note = record.note
  modalVisible.value = true
}

const deleteAutoFocus = (record: AutoFunction) => {
  Modal.confirm({
    title: 'Delete Auto Function',
    content: `Are you sure you want to delete "${record.name}"?`,
    okText: 'Delete',
    okType: 'danger',
    cancelText: 'Cancel',
    onOk: async () => {
      try {
        deleting.value = true
        console.log('Deleting auto focus:', record.id, record.name)

        await deleteAutoFocusService(record.id)
        console.log('Delete successful, reloading data...')

        message.success('Auto focus deleted successfully')

        // Reload data to refresh the table
        await loadAutoFocusItems()
        console.log('Data reloaded after delete')

        // Reset pagination to first page if current page becomes empty
        const totalPages = Math.ceil(autoFocusItems.value.length / pagination.pageSize)
        if (pagination.current > totalPages && totalPages > 0) {
          pagination.current = totalPages
        }
      } catch (error) {
        message.error(`Failed to delete auto focus: ${error.message || error}`)
        console.error('Error deleting auto focus:', error)
      } finally {
        deleting.value = false
      }
    },
  })
}

const handleSave = async () => {
  try {
    console.log('Starting save process...')
    console.log('Form data:', formData)

    // Manual validation check
    if (!formData.code || formData.code.trim().length < 2) {
      message.error('Please enter a valid code (at least 2 characters)')
      return
    }

    if (!formData.name || formData.name.trim().length < 2) {
      message.error('Please enter a valid name (at least 2 characters)')
      return
    }

    if (!formData.customerIds || formData.customerIds.length === 0) {
      message.error('Please select at least one customer')
      return
    }

    // Try form validation but don't fail if it doesn't work
    try {
      await formRef.value?.validate()
      console.log('Form validation passed')
    } catch (validationError) {
      console.log('Form validation failed, but continuing with manual validation:', validationError)
    }

    saving.value = true

    if (isEditing.value && editingId.value) {
      console.log('Updating auto focus:', editingId.value, formData)
      await updateAutoFocus(editingId.value, formData)
      message.success('Auto focus updated successfully')
    } else {
      console.log('Creating new auto focus:', formData)
      const result = await createAutoFocus(formData)
      console.log('Create result:', result)
      message.success('Auto focus created successfully')
    }

    modalVisible.value = false
    resetForm() // Clear form data after successful save
    await loadAutoFocusItems()
  } catch (error) {
    console.error('Save error details:', error)
    message.error(`Failed to save auto focus: ${error.message || error}`)
    console.error('Error saving auto focus:', error)
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

const resetForm = () => {
  formData.code = ''
  formData.name = ''
  formData.customerIds = []
  formData.note = ''
  formRef.value?.resetFields()
}

const handleSelectAllCustomers = (e: any) => {
  if (e.target.checked) {
    // Select all customers
    formData.customerIds = customers.value.map((customer) => customer.id)
  } else {
    // Deselect all customers
    formData.customerIds = []
  }
}

const filterCustomerOption = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase())
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

// Initialize
onMounted(async () => {
  try {
    loading.value = true
    console.log('Initializing Auto Function page...')

    // Database should already be initialized in App.vue
    console.log('Loading customers...')
    await loadCustomers()

    console.log('Loading auto focus items...')
    await loadAutoFocusItems(false) // Don't show loading for this call since we're managing it here

    console.log('Auto Function page initialization completed')
  } catch (error) {
    console.error('Error initializing Auto Function page:', error)
    message.error('Failed to initialize page data')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.auto-focus-management {
  padding: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.filters {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.note-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.customer-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 380px;
}

.customer-tags .ant-tag {
  margin: 0;
  font-size: 12px;
  padding: 2px 6px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-dropdown-menu-item) {
  display: flex;
  align-items: center;
  gap: 8px;
}

.customer-select-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.select-all-checkbox {
  margin-bottom: 8px;
}

.customer-select {
  width: 100%;
}

.all-customers-tag {
  font-weight: 600;
  font-size: 13px;
  padding: 4px 12px;
}
</style>
