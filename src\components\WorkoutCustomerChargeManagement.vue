<template>
  <div class="workout-customer-charge-management">
    <div class="header">
      <h2>Custom Workout Charge</h2>
    </div>

    <!-- Filters -->
    <div class="filters">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-input
            v-model:value="filters.search"
            placeholder="Search by customer, division, or category"
            allow-clear
            @change="loadWorkoutCharges"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
      </a-row>
    </div>

    <!-- Table -->
    <a-table
      :columns="columns"
      :data-source="workoutCharges"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'unit_price'">
          {{ formatCurrency(record.unit_price) }}
        </template>
        <template v-else-if="column.key === 'actions'">
          <ActionButtons
            name="Workout Charge"
            :show-save="false"
            :show-edit="true"
            :show-delete="true"
            :show-view="false"
            @edit="editWorkoutCharge(record)"
            @delete="deleteWorkoutCharge(record)"
          />
        </template>
      </template>
    </a-table>

    <!-- Create/Edit Modal -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEditing ? 'Edit Workout Charge ssss' : 'Create Workout Charge'"
      :confirm-loading="saving"
      :ok-text="isEditing ? 'Update' : 'Create'"
      cancel-text="Cancel"
      width="800px"
      @ok="handleSave"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="Customer" name="customer_id" required>
              <a-input
                :value="displayCustomerName"
                placeholder="Customer will be auto-selected"
                readonly
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Division" name="division_id" required>
              <a-input
                :value="displayDivisionName"
                placeholder="Division will be auto-selected"
                readonly
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="Category" name="cate_id" required>
              <a-select
                v-model:value="formData.cate_id"
                placeholder="Select category"
                @change="onCategoryChange"
              >
                <a-select-option
                  v-for="category in categories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Category Workout" name="cate_workout_id" required>
              <a-select
                v-model:value="formData.cate_workout_id"
                placeholder="Select category workout"
                :disabled="!formData.cate_id"
                @change="onCategoryWorkoutChange"
              >
                <a-select-option
                  v-for="workout in categoryWorkouts"
                  :key="workout.id"
                  :value="workout.id"
                >
                  {{ workout.name }} - {{ formatCurrency(workout.unit_price) }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="Unit Price" name="unit_price">
          <a-input-number
            v-model:value="formData.unit_price"
            :min="0"
            :precision="2"
            style="width: 100%"
            :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
            placeholder="Unit price will be auto-filled"
            disabled
          />
        </a-form-item>

        <!-- Workout Charge Content -->
        <a-form-item label="Workout Charge Details">
          <div class="charge-content">
            <a-button type="dashed" @click="addYear" style="margin-bottom: 16px">
              <template #icon>
                <PlusOutlined />
              </template>
              Add Year  sss
            </a-button>

            <div
              v-for="(yearData, year) in formData.workout_customer_charge_content"
              :key="year"
              class="year-section"
            >
              <div class="year-header" style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center; gap: 8px;">
                  <h4 style="margin: 0; color: #1890ff;">Year {{ year }}</h4>
                </div>
                <div style="display: flex; align-items: center; gap: 8px; margin-left: auto;">
                  <span style="font-size: 13px; color: #888;">Quick fill:</span>
                  <a-input-number
                    v-model:value="quickFillValues[year]"
                    :min="0"
                    :precision="2"
                    size="small"
                    placeholder="Value"
                    style="width: 70px;"
                  />
                  <a-button
                    type="default"
                    size="small"
                    :disabled="quickFillValues[year] === undefined || quickFillValues[year] === null"
                    @click="fillAllMonths(year)"
                  >
                    Fill All
                  </a-button>
                  <a-button
                    type="default"
                    size="small"
                    :disabled="quickFillValues[year] === undefined || quickFillValues[year] === null"
                    @click="quickFillValues[year] = undefined"
                  >
                    Clear
                  </a-button>
                  <a-button type="text" danger @click="removeYear(year)">
                    <template #icon>
                      <DeleteOutlined />
                    </template>
                    Remove Year
                  </a-button>
                </div>
              </div>

              <a-button
                type="dashed"
                size="small"
                @click="addMonth(year)"
                style="margin-bottom: 8px"
              >
                Add Month
              </a-button>

              <div v-for="(monthData, index) in yearData" :key="index" class="month-row">
                <a-row :gutter="8" align="middle">
                  <a-col :span="3">
                    <a-select v-model:value="monthData.month" placeholder="Month" size="small">
                      <a-select-option v-for="month in 12" :key="month" :value="month">
                        {{ getMonthName(month) }}
                      </a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="4">
                    <a-input-number
                      v-model:value="monthData.quantity"
                      :min="0"
                      :precision="2"
                      placeholder="Quantity"
                      size="small"
                      style="width: 100%"
                      @change="calculateNetAmount(year, index)"
                    />
                  </a-col>
                  <a-col :span="4">
                    <a-input-number
                      :value="monthData.net_amount"
                      placeholder="Net Amount"
                      size="small"
                      style="width: 100%"
                      disabled
                      :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    />
                  </a-col>
                  <a-col :span="4">
                    <a-input-number
                      v-model:value="monthData.vat"
                      :min="0"
                      :max="1"
                      :precision="4"
                      placeholder="VAT"
                      size="small"
                      style="width: 100%"
                      @change="calculateVatAmount(year, index)"
                    />
                  </a-col>
                  <a-col :span="4">
                    <a-input-number
                      :value="monthData.vat_amount"
                      placeholder="VAT Amount"
                      size="small"
                      style="width: 100%"
                      disabled
                      :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    />
                  </a-col>
                  <a-col :span="2">
                    <a-button type="text" danger size="small" @click="removeMonth(year, index)">
                      <template #icon>
                        <DeleteOutlined />
                      </template>
                    </a-button>
                  </a-col>
                </a-row>
              </div>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { TableColumnsType } from 'ant-design-vue'
import { PlusOutlined, SearchOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import type {
  WorkoutCustomerCharge,
  WorkoutCustomerChargeFormData,
  WorkoutCustomerChargeFilters,
  CategoryOption,
  CategoryWorkoutOption,
  CustomerOption,
  DivisionOption,
} from '../types/WorkoutCustomerCharge'
import {
  getWorkoutCustomerCharges,
  createWorkoutCustomerCharge,
  updateWorkoutCustomerCharge,
  deleteWorkoutCustomerCharge as deleteWorkoutCustomerChargeService,
  getCustomersForDropdown,
  getDivisionsByCustomerId,
  getCategoriesForDropdown,
  getCategoryWorkoutsByCategoryId,
} from '../services/master-data/workoutCustomerCharge/workoutCustomerChargeService'
import ActionButtons from './ActionButtons.vue'

// Props
interface Props {
  customerId?: number
  divisionId?: number
  customerName?: string
  divisionName?: string
}

const props = withDefaults(defineProps<Props>(), {
  customerId: undefined,
  divisionId: undefined,
  customerName: undefined,
  divisionName: undefined,
})

// Reactive data
const loading = ref(false)
const saving = ref(false)
const modalVisible = ref(false)
const isEditing = ref(false)
const editingId = ref<string | null>(null)
const formRef = ref()

const workoutCharges = ref<WorkoutCustomerCharge[]>([])
const customers = ref<CustomerOption[]>([])
const divisions = ref<DivisionOption[]>([])
const categories = ref<CategoryOption[]>([])
const categoryWorkouts = ref<CategoryWorkoutOption[]>([])

const filters = reactive<WorkoutCustomerChargeFilters>({
  search: '',
  customer_id: props.customerId,
  division_id: props.divisionId,
})

const formData = reactive<WorkoutCustomerChargeFormData>({
  customer_id: props.customerId || 0,
  division_id: props.divisionId || 0,
  cate_id: '',
  cate_workout_id: '',
  unit_price: 0,
  workout_customer_charge_content: {},
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
})

// Form validation rules
const rules = {
  customer_id: [{ required: true, message: 'Please select customer' }],
  division_id: [{ required: true, message: 'Please select division' }],
  cate_id: [{ required: true, message: 'Please select category' }],
  cate_workout_id: [{ required: true, message: 'Please select category workout' }],
}

// Table columns
const columns: TableColumnsType = [
  {
    title: 'Customer',
    dataIndex: 'customer_name',
    key: 'customer_name',
    width: 150,
  },
  {
    title: 'Division',
    dataIndex: 'division_name',
    key: 'division_name',
    width: 150,
  },
  {
    title: 'Category ID',
    dataIndex: 'cate_id',
    key: 'cate_id',
    width: 120,
  },
  {
    title: 'Workout ID',
    dataIndex: 'cate_workout_id',
    key: 'cate_workout_id',
    width: 120,
  },
  {
    title: 'Unit Price',
    dataIndex: 'unit_price',
    key: 'unit_price',
    width: 120,
    align: 'right',
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 80,
    align: 'center',
  },
]

// Helper functions
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(value)
}

const getMonthName = (month: number): string => {
  const months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ]
  return months[month - 1] || `Month ${month}`
}

// Computed properties
const displayCustomerName = computed(() => {
  if (props.customerName) {
    return props.customerName
  }
  if (props.customerId) {
    const customer = customers.value.find((c) => c.id === props.customerId)
    return customer ? `${customer.name} (${customer.code})` : `Customer ID: ${props.customerId}`
  }
  return 'No customer selected'
})

const displayDivisionName = computed(() => {
  if (props.divisionName) {
    return props.divisionName
  }
  if (props.divisionId) {
    const division = divisions.value.find((d) => d.id === props.divisionId)
    return division ? `${division.name} (${division.code})` : `Division ID: ${props.divisionId}`
  }
  return 'No division selected'
})

// Methods
const loadWorkoutCharges = async (showLoading = true) => {
  try {
    if (showLoading) loading.value = true

    const items = await getWorkoutCustomerCharges(filters)
    workoutCharges.value = items
    pagination.total = items.length
  } catch (error) {
    message.error('Failed to load workout charges')
  } finally {
    if (showLoading) loading.value = false
  }
}

const loadDropdownData = async () => {
  try {
    const [customersData, categoriesData] = await Promise.all([
      getCustomersForDropdown(),
      getCategoriesForDropdown(),
    ])

    customers.value = customersData
    categories.value = categoriesData

    // Load divisions if customer is pre-selected
    if (props.customerId) {
      const divisionsData = await getDivisionsByCustomerId(props.customerId)
      divisions.value = divisionsData
    }
  } catch (error) {
    console.error('Error loading dropdown data:', error)
  }
}

// onCustomerChange removed since customer/division are readonly

const onCategoryChange = async (categoryId: string) => {
  console.log('Category changed to:', categoryId)

  formData.cate_workout_id = ''
  formData.unit_price = 0
  categoryWorkouts.value = []

  if (categoryId) {
    try {
      const workoutsData = await getCategoryWorkoutsByCategoryId(categoryId)
      console.log('Received category workouts:', workoutsData)
      categoryWorkouts.value = workoutsData
      console.log('Category workouts set to:', categoryWorkouts.value)
    } catch (error) {
      console.error('Error loading category workouts:', error)
    }
  } else {
    console.log('No category selected, clearing workouts')
  }
}

const onCategoryWorkoutChange = (workoutId: string) => {
  const selectedWorkout = categoryWorkouts.value.find((w) => w.id === workoutId)
  if (selectedWorkout) {
    formData.unit_price = selectedWorkout.unit_price
  }
}

const addYear = () => {
  const currentYear = new Date().getFullYear()
  const newYear = currentYear.toString()

  if (!formData.workout_customer_charge_content[newYear]) {
    formData.workout_customer_charge_content[newYear] = []
  }
}

const removeYear = (year: string) => {
  delete formData.workout_customer_charge_content[year]
}

const addMonth = (year: string) => {
  if (!formData.workout_customer_charge_content[year]) {
    formData.workout_customer_charge_content[year] = []
  }

  formData.workout_customer_charge_content[year].push({
    month: 1,
    quantity: 0,
    net_amount: 0,
    vat: 0.1, // Default 10% VAT
    vat_amount: 0,
  })
}

const removeMonth = (year: string, index: number) => {
  formData.workout_customer_charge_content[year].splice(index, 1)

  // Remove year if no months left
  if (formData.workout_customer_charge_content[year].length === 0) {
    delete formData.workout_customer_charge_content[year]
  }
}

const calculateNetAmount = (year: string, index: number) => {
  const monthData = formData.workout_customer_charge_content[year][index]
  monthData.net_amount = (monthData.quantity || 0) * formData.unit_price
  calculateVatAmount(year, index)
}

const calculateVatAmount = (year: string, index: number) => {
  const monthData = formData.workout_customer_charge_content[year][index]
  monthData.vat_amount = monthData.net_amount * (monthData.vat || 0)
}

const editWorkoutCharge = (record: WorkoutCustomerCharge) => {
  isEditing.value = true
  editingId.value = record.id
  formData.customer_id = record.customer_id
  formData.division_id = record.division_id
  formData.cate_id = record.cate_id
  formData.cate_workout_id = record.cate_workout_id
  formData.unit_price = record.unit_price
  formData.workout_customer_charge_content = { ...record.workout_customer_charge_content }

  // Load related data
  onCategoryChange(record.cate_id)

  modalVisible.value = true
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()
    saving.value = true

    // Ensure customer_id and division_id are set from props if not already set
    if (!formData.customer_id && props.customerId) {
      formData.customer_id = props.customerId
    }
    if (!formData.division_id && props.divisionId) {
      formData.division_id = props.divisionId
    }

    console.log('Saving workout charge with data:', {
      customer_id: formData.customer_id,
      division_id: formData.division_id,
      cate_id: formData.cate_id,
      cate_workout_id: formData.cate_workout_id,
      unit_price: formData.unit_price,
      content_keys: Object.keys(formData.workout_customer_charge_content),
    })

    // Validate required fields
    if (!formData.customer_id) {
      throw new Error('Customer is required')
    }
    if (!formData.division_id) {
      throw new Error('Division is required')
    }
    if (!formData.cate_id) {
      throw new Error('Category is required')
    }
    if (!formData.cate_workout_id) {
      throw new Error('Category Workout is required')
    }

    if (isEditing.value && editingId.value) {
      const updatedCharge = await updateWorkoutCustomerCharge(editingId.value, formData)
      const index = workoutCharges.value.findIndex((item) => item.id === editingId.value)
      if (index !== -1) {
        workoutCharges.value[index] = updatedCharge
      }
      message.success('Workout charge updated successfully')
    } else {
      const newCharge = await createWorkoutCustomerCharge(formData)
      workoutCharges.value.unshift(newCharge)
      pagination.total = workoutCharges.value.length
      message.success('Workout charge created successfully')
    }

    modalVisible.value = false
    resetForm()
  } catch (error) {
    console.error('Save error:', error)
    message.error(`Failed to save workout charge: ${error.message || error}`)
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

const resetForm = () => {
  // Ensure customer_id and division_id are properly set from props
  formData.customer_id = props.customerId || 0
  formData.division_id = props.divisionId || 0
  formData.cate_id = ''
  formData.cate_workout_id = ''
  formData.unit_price = 0
  formData.workout_customer_charge_content = {}
  divisions.value = []
  categoryWorkouts.value = []
  formRef.value?.resetFields()

  console.log(
    'Form reset with customer_id:',
    formData.customer_id,
    'division_id:',
    formData.division_id,
  )
}

const deleteWorkoutCharge = (record: WorkoutCustomerCharge) => {
  Modal.confirm({
    title: 'Delete Workout Charge',
    content: `Are you sure you want to delete this workout charge?`,
    okText: 'Delete',
    okType: 'danger',
    cancelText: 'Cancel',
    onOk: async () => {
      try {
        await deleteWorkoutCustomerChargeService(record.id)
        const index = workoutCharges.value.findIndex((item) => item.id === record.id)
        if (index !== -1) {
          workoutCharges.value.splice(index, 1)
          pagination.total = workoutCharges.value.length
        }
        message.success('Workout charge deleted successfully')
      } catch (error) {
        message.error(`Failed to delete workout charge: ${error.message || error}`)
        console.error('Error deleting workout charge:', error)
      }
    },
  })
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

// Initialize
onMounted(async () => {
  try {
    loading.value = true
    await loadDropdownData()
    await loadWorkoutCharges(false)
  } catch (error) {
    console.error('Error initializing workout customer charge management:', error)
    message.error('Failed to initialize page data')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.workout-customer-charge-management {
  padding: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.filters {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.charge-content {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.year-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background: white;
}

.year-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.year-header h4 {
  margin: 0;
  color: #1890ff;
}

.month-row {
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background: #fafafa;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}
</style>
