<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { theme } from 'ant-design-vue'
import { computed, onMounted, ref } from 'vue'
import SideBar from '@IVC/components/SideBar.vue'
import ChangePasswordModal from '@IVC/components/ChangePasswordModal.vue'
import UserDropdown from '@IVC/components/UserDropdown.vue'
import { useAuthStore } from '@IVC/stores/auth'
import { useAuthService } from '@IVC/services/auth'
import { seedInitialUsers } from '@IVC/services/master-data/user/index'
import { getCompanyProfile } from '@IVC/services/master-data/companyProfile/companyProfileService'
import { useDefaultStore } from '@IVC/stores/useDefaultStore'
import { initializeAutoFocusDB } from '@IVC/services/master-data/autoFunction/autoFunctionService'
import { initializeCODDB } from '@IVC/services/master-data/cod/codService'
import { initializeWorkoutCustomerChargeDB } from '@IVC/services/master-data/workoutCustomerCharge/workoutCustomerChargeService'

const { defaultAlgorithm, defaultSeed } = theme
const mapToken = defaultAlgorithm(defaultSeed)
const route = useRoute()
const authStore = useAuthStore()
const authService = useAuthService()
const { defaultStoreName, updateDefaultStoreName } = useDefaultStore()

/**
 * Check if current route is login page
 * */
const isLoginPage = computed(() => route.name === 'login')

// Change password modal state
const changePasswordVisible = ref(false)

/**
 * Initialize auth on app start
 * */
onMounted(async () => {
  // Seed initial users if needed
  await seedInitialUsers()

  // Initialize auto function database
  try {
    console.log('Initializing Auto Function database...')
    await initializeAutoFocusDB()
    console.log('Auto Function database initialized successfully')
  } catch (error) {
    console.error('Error initializing Auto Function database:', error)
  }

  // Initialize COD database
  try {
    console.log('Initializing COD database...')
    await initializeCODDB()
    console.log('COD database initialized successfully')
  } catch (error) {
    console.error('Error initializing COD database:', error)
  }

  // Initialize Workout Customer Charge database
  try {
    console.log('Initializing Workout Customer Charge database...')
    // await initializeWorkoutCustomerChargeDB()
    console.log('Workout Customer Charge database initialized successfully')
  } catch (error) {
    console.error('Error initializing Workout Customer Charge database:', error)
  }

  // Initialize auth
  authStore.initializeAuth()

  // Load default store name
  try {
    const profile = await getCompanyProfile()
    const defaultStore = profile.stores.find((store: any) => store.isDefault)
    if (defaultStore && defaultStore.name) {
      updateDefaultStoreName(defaultStore.name)
    } else {
      // Fallback to first store if no default found
      const firstStore = profile.stores?.[0]
      if (firstStore && firstStore.name) {
        updateDefaultStoreName(firstStore.name)
        console.log('Using first store as default:', firstStore.name)
      } else {
        // Ultimate fallback
        updateDefaultStoreName('Default Warehouse')
        console.log('Using fallback warehouse name')
      }
    }
  } catch (error) {
    console.error('Error loading company profile:', error)
    // Set fallback name if company profile fails to load
    updateDefaultStoreName('Default Warehouse')
  }
})

// Handle logout
const handleLogout = () => {
  authService.logout()
}

// Show change password modal
const showChangePasswordModal = () => {
  changePasswordVisible.value = true
}

// Handle change password success
const handleChangePasswordSuccess = () => {
  // Additional logic after password change if needed
  console.log('Password changed successfully from parent component')
}
</script>

<template>
  <a-config-provider
    :theme="{
      token: {
        colorPrimary: '#00b96b',
        algorithm: theme.defaultAlgorithm,
      },
    }"
  >
    <!-- Login Page Layout (Simple) -->
    <div v-if="isLoginPage" style="height: 100vh">
      <RouterView />
    </div>

    <!-- Dashboard Layout (Full) -->
    <a-layout v-else has-sider style="gap: 16px">
      <SideBar />
      <a-layout
        :style="{
          height: '100dvh',
          display: 'flex',
          flexDirection: 'column',
        }"
      >
        <a-layout-header
          style="
            background: #f5f5f5;
            padding: 0;
            margin: 0px 16px;
            display: flex;
            justify-content: space-between;
          "
        >
          <h2 style="margin: 0">{{ defaultStoreName }}</h2>
          <div style="display: flex; align-items: center; gap: 12px">
            <UserDropdown
              :user="authStore.user"
              @change-password="showChangePasswordModal"
              @logout="handleLogout"
            />
          </div>
        </a-layout-header>
        <a-layout-content :style="{ margin: '0px 16px' }">
          <div
            :style="{
              padding: '24px',
              background: '#fff',
              height: '100%',
              overflow: 'auto',
              borderRadius: mapToken.borderRadiusLG + 'px',
            }"
          >
            <RouterView />
          </div>
        </a-layout-content>
        <a-layout-footer :style="{ textAlign: 'center' }">
          ISB VIETNAM COMPANY ©{{ new Date().getFullYear() }}
        </a-layout-footer>
      </a-layout>
    </a-layout>

    <!-- Change Password Modal -->
    <ChangePasswordModal
      v-model:open="changePasswordVisible"
      @success="handleChangePasswordSuccess"
    />
  </a-config-provider>
</template>

<style scoped></style>
