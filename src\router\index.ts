import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@IVC/stores/auth'
import { message } from 'ant-design-vue'
import HomeView from '@IVC/views/HomeView.vue'
import ImportInbound from '@IVC/views/Invoice/Import/ImportInboundView.vue'
import ImportOutbound from '@IVC/views/Invoice/Import/ImportOutboundView.vue'
import InboundListView from '@IVC/views/Invoice/InboundListView.vue'
import OutboundListView from '@IVC/views/Invoice/OutboundListView.vue'
import CustomerView from '@IVC/views/MasterDataView/CustomerView.vue'
import UnitView from '@IVC/views/MasterDataView/UnitView.vue'
import LoginView from '@IVC/views/LoginView.vue'
import UserManagementView from '@IVC/views/UserManagementView.vue'
import InvoiceListView from '@IVC/views/InvoiceListView.vue'
import CompanyProfileView from '@IVC/views/CompanyProfileView.vue'
import DashboardView from '@IVC/views/DashboardView.vue'
import {
  MailOutlined,
  SettingOutlined,
  ProfileOutlined,
  ImportOutlined,
  ExportOutlined,
  UserOutlined,
  DollarCircleOutlined,
  UnorderedListOutlined,
  DashboardOutlined,
  BarChartOutlined,
} from '@ant-design/icons-vue'
import CategoryView from '@IVC/views/MasterDataView/CategoryView.vue'
import ProductView from '@IVC/views/MasterDataView/ProductView.vue'
import ProductImportView from '@IVC/views/MasterDataView/ProductImportView.vue' // Import mới
import AutoFunctionView from '@IVC/views/AutoFunctionView.vue'

declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    icon?: any
    hidden?: boolean
    requiresAuth?: boolean
    requiredRole?: 'admin' | 'manager' | 'user'
  }
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/dashboard',
      name: 'dashboard',
      component: DashboardView,
      meta: {
        title: 'Dashboard',
        icon: DashboardOutlined,
        requiresAuth: true,
      },
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: {
        title: 'Login',
        hidden: true,
        requiresAuth: false,
      },
    },
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: 'Home',
        icon: MailOutlined,
        requiresAuth: true,
      },
    },
    {
      path: '/Invoice',
      name: 'Invoice',
      meta: {
        title: 'Invoice',
        icon: ProfileOutlined,
        requiresAuth: true,
      },
      children: [
        {
          path: 'ImportInbound',
          name: 'Import Inbound',
          component: ImportInbound,
          meta: {
            title: 'Import Inbound',
            icon: ImportOutlined,
            requiresAuth: true,
          },
        },
        {
          path: 'ImportOutbound',
          name: 'Import Outbound',
          component: ImportOutbound,
          meta: {
            title: 'Import Outbound',
            icon: ExportOutlined,
            requiresAuth: true,
          },
        },
        {
          path: 'InboundList',
          name: 'Inbound List',
          component: InboundListView,
          meta: {
            title: 'Inbound List',
            icon: UnorderedListOutlined,
            requiresAuth: true,
          },
        },
        {
          path: 'OutboundList',
          name: 'Outbound List',
          component: OutboundListView,
          meta: {
            title: 'Outbound List',
            icon: UnorderedListOutlined,
            requiresAuth: true,
          },
        },
      ],
    },
    {
      path: '/master-data',
      name: 'master-data',
      redirect: '/master-data/customer',
      meta: {
        title: 'Master Data',
        icon: SettingOutlined,
        requiresAuth: true,
      },
      children: [
        {
          path: '/users',
          name: 'users',
          component: UserManagementView,
          meta: {
            title: 'User',
            requiresAuth: true,
            requiredRole: 'admin',
          },
        },
        {
          path: 'customer',
          name: 'customer',
          component: CustomerView,
          meta: {
            title: 'Customer',
            requiresAuth: true,
          },
        },
        {
          path: 'unit',
          name: 'unit',
          component: UnitView,
          meta: {
            title: 'Unit',
            requiresAuth: true,
          },
        },
        {
          path: 'category',
          name: 'category',
          component: CategoryView,
          meta: {
            title: 'Category',
            requiresAuth: true,
          },
        },
        {
          path: 'product/list',
          name: 'product',
          component: ProductView,
          meta: {
            title: 'Product List',
            requiresAuth: true,
            requiredRole: 'admin',
          },
        },
        {
          path: 'product/import',
          name: 'ProductImport',
          component: ProductImportView,
          meta: {
            title: 'Import Products',
            requiresAuth: true,
            requiredRole: 'admin',
          },
        },
        {
          path: 'auto-focus',
          name: 'auto-focus',
          component: AutoFunctionView,
          meta: {
            title: 'Auto Function',
            requiresAuth: true,
          },
        },
        {
          path: 'cod',
          name: 'cod',
          component: () => import('@IVC/views/CODManagementView.vue'),
          meta: {
            title: 'Delivery Code',
            requiresAuth: true,
          },
        },
        {
          path: '/company-profile',
          name: 'company-profile',
          component: CompanyProfileView,
          meta: {
            title: 'Company Profile',
            requiresAuth: true,
            requiredRole: 'admin',
          },
        },
      ],
    },
    {
      path: '/invoice-list',
      name: 'Invoice List',
      component: InvoiceListView,
      meta: {
        title: 'Warehouse Charges',
        icon: DollarCircleOutlined,
        requiresAuth: true,
      },
    },
    {
      path: '/invoice-detail',
      name: 'invoice-detail',
      component: () => import('@IVC/views/InvoiceDetailView.vue'),
      meta: { title: 'Invoice Detail', requiresAuth: true, hidden: true },
    },
    {
      path: '/reports',
      name: 'Reports',
      component: () => import('../views/ReportView.vue'),
      meta: {
        title: 'Reports',
        icon: BarChartOutlined,
        requiresAuth: true,
      },
    },
  ],
})

// Navigation guard
router.beforeEach((to, _from, next) => {
  const authStore = useAuthStore()

  // Initialize auth from localStorage if not already done
  if (!authStore.isAuthenticated) {
    authStore.initializeAuth()
  }

  const requiresAuth = to.meta.requiresAuth !== false // Default to true
  const isAuthenticated = authStore.isAuthenticated
  const requiredRole = to.meta.requiredRole
  const userRole = authStore.user?.role

  if (requiresAuth && !isAuthenticated) {
    // Redirect to login if route requires auth and user is not authenticated
    next('/login')
  } else if (to.name === 'login' && isAuthenticated) {
    // Redirect to home if user is already authenticated and trying to access login
    next('/')
  } else if (requiredRole && userRole !== requiredRole) {
    // Check role-based access
    if (userRole === 'admin') {
      // Admin can access everything
      next()
    } else if (userRole === 'manager' && requiredRole !== 'admin') {
      // Manager can access manager and user routes
      next()
    } else if (userRole === 'user' && requiredRole === 'user') {
      // User can only access user routes
      next()
    } else {
      // Access denied - redirect to home with error
      console.warn(
        `Access denied: User role '${userRole}' cannot access route requiring '${requiredRole}'`,
      )
      message.error(`Access denied. You need ${requiredRole} role to access this page.`)
      next('/')
    }
  } else {
    // Allow navigation
    next()
  }
})

export default router
