import { useAuthStore } from '@IVC/stores/auth'
import { useRouter } from 'vue-router'
import { type LoginCredentials, type LoginResponse } from '@IVC/types/login.ts'
import { authenticateUser, getAllUsers } from '@IVC/services/master-data/user/index'

export const useAuthService = () => {
  const authStore = useAuthStore()
  const router = useRouter()

  const login = async (credentials: LoginCredentials): Promise<LoginResponse> => {
    authStore.setLoading(true)

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Authenticate user using SQLite database
      const authenticatedUser = await authenticateUser(credentials.username, credentials.password)

      if (authenticatedUser) {
        // Generate a mock JWT token
        const token = `mock_jwt_token_${Date.now()}_${authenticatedUser.id}`

        // Convert SQLite User to Login User format
        const loginUser = {
          id: authenticatedUser.id,
          username: authenticatedUser.username,
          email: authenticatedUser.email,
          role: authenticatedUser.role,
          name: authenticatedUser.name,
        }

        // Set auth data in store
        authStore.setAuth(loginUser, token)

        return {
          success: true,
          user: loginUser,
          token,
          message: `Welcome back, ${authenticatedUser.name}!`,
        }
      } else {
        return {
          success: false,
          message: 'Invalid username or password',
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        message: 'Login failed. Please try again.',
      }
    } finally {
      authStore.setLoading(false)
    }
  }

  const logout = async () => {
    authStore.setLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500))

      // Clear auth data
      authStore.clearAuth()

      // Redirect to login
      await router.push('/login')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      authStore.setLoading(false)
    }
  }

  const getDefaultCredentials = async () => {
    try {
      const users = await getAllUsers()
      return users.map((user) => ({
        username: user.username,
        password: user.password,
        role: user.role,
        description: `${user.role.charAt(0).toUpperCase() + user.role.slice(1)} access`,
      }))
    } catch (error) {
      console.error('Error getting default credentials:', error)
      return []
    }
  }

  return {
    login,
    logout,
    getDefaultCredentials,
  }
}
