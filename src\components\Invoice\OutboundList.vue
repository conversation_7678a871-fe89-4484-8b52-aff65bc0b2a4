<template>
  <div class="outbound-list-container">
    <div class="page-header">
      <h2>Outbound List</h2>
      <p>View and search imported outbound records</p>
    </div>

    <!-- Search Filters -->
    <div class="search-section">
      <a-card title="Search Filters" class="search-card">
        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="Customer">
                <a-select
                  v-model:value="searchFilters.customerId"
                  placeholder="Select customer"
                  allow-clear
                  show-search
                  :filter-option="filterCustomerOption"
                  @change="handleCustomerChange"
                >
                  <a-select-option
                    v-for="customer in customers"
                    :key="customer.id"
                    :value="customer.id"
                  >
                    {{ customer.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="Division">
                <a-select
                  v-model:value="searchFilters.divisionId"
                  placeholder="Select division"
                  allow-clear
                  show-search
                  :filter-option="filterDivisionOption"
                  @change="handleDivisionChange"
                  :disabled="!searchFilters.customerId"
                >
                  <a-select-option
                    v-for="division in filteredDivisions"
                    :key="division.id"
                    :value="division.id"
                  >
                    {{ division.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="Month">
                <a-date-picker
                  v-model:value="searchFilters.month"
                  picker="month"
                  placeholder="Select month"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  @change="handleMonthChange"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="Actions">
                <a-space>
                  <a-button type="primary" @click="searchData" :loading="isLoading">
                    <template #icon>
                      <SearchOutlined />
                    </template>
                    Search
                  </a-button>
                  <a-button @click="clearFilters">
                    <template #icon>
                      <ClearOutlined />
                    </template>
                    Clear
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- Results Summary -->
    <div v-if="outboundData.length > 0 || hasSearched" class="results-summary">
      <a-alert
        :message="`Found ${pagination.total} records`"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />
    </div>

    <!-- Error Message -->
    <a-alert
      v-if="errorMessage"
      type="error"
      :message="errorMessage"
      show-icon
      closable
      @close="errorMessage = ''"
      style="margin-bottom: 16px"
    />

    <!-- Data Table -->
    <div class="table-section">
      <a-card>
        <template #title>
          <a-space>
            <span>Outbound Records</span>
            <a-tag v-if="hasSearched" color="blue">{{ pagination.total }} records</a-tag>
          </a-space>
        </template>

        <template #extra>
          <a-space>
            <a-button @click="refreshData" :loading="isLoading">
              <template #icon>
                <ReloadOutlined />
              </template>
              Refresh
            </a-button>
            <a-button type="primary" @click="exportData" :disabled="outboundData.length === 0">
              <template #icon>
                <DownloadOutlined />
              </template>
              Export
            </a-button>
          </a-space>
        </template>

        <a-table
          :dataSource="outboundData"
          :columns="tableColumns"
          :pagination="paginationConfig"
          :loading="isLoading"
          :scroll="{ x: 2500 }"
          size="middle"
          @change="handleTableChange"
        >
          <!-- Custom render for date column -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'sph_ship_ymd'">
              <a-tag color="green">
                {{ formatDate(record.sph_ship_ymd) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'customer_name'">
              <a-space>
                <a-avatar size="small" style="background-color: #52c41a">
                  {{ record.customer_name?.charAt(0) || 'N' }}
                </a-avatar>
                <span>{{ record.customer_name || 'Unknown' }}</span>
              </a-space>
            </template>
            <template v-else-if="column.key === 'division_name'">
              <a-tag v-if="record.division_name" color="blue">
                {{ record.division_name }}
              </a-tag>
              <span v-else style="color: #999">N/A</span>
            </template>
            <template v-else-if="column.key === 'created_at'">
              <span style="color: #666; font-size: 12px">
                {{ formatDateTime(record.created_at) }}
              </span>
            </template>
            <template v-else-if="column.key === 'actions'">
              <ActionButtons
                name="Record"
                :show-edit="false"
                :show-view="true"
                :show-delete="true"
                :show-save="false"
                :show-download="false"
                :show-add-customer-divisions="false"
                :show-setting-button="false"
                :show-add-workout="false"
                @view="viewRecord(record)"
                @delete="deleteRecord(record)"
              />
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- Record Detail Modal -->
    <a-modal
      v-model:open="showDetailModal"
      title="Outbound Record Details"
      :width="800"
      :footer="null"
    >
      <div v-if="selectedRecord" class="record-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="ID">{{ selectedRecord.id }}</a-descriptions-item>
          <a-descriptions-item label="Customer">{{
            selectedRecord.customer_name
          }}</a-descriptions-item>
          <a-descriptions-item label="Division">{{
            selectedRecord.division_name || 'N/A'
          }}</a-descriptions-item>
          <a-descriptions-item label="SPR AS Number">{{
            selectedRecord.spr_as_num
          }}</a-descriptions-item>
          <a-descriptions-item label="SPR ASLN Number">{{
            selectedRecord.spr_asln_num
          }}</a-descriptions-item>
          <a-descriptions-item label="SPR ASSQ Number">{{
            selectedRecord.spr_assq_num
          }}</a-descriptions-item>
          <a-descriptions-item label="Ship Date">{{
            formatDate(selectedRecord.sph_ship_ymd)
          }}</a-descriptions-item>
          <a-descriptions-item label="Delivery Code">{{
            selectedRecord.sph_dlv_cod
          }}</a-descriptions-item>
          <a-descriptions-item label="Delivery Name">{{
            selectedRecord.sph_dlv_nam1
          }}</a-descriptions-item>
          <a-descriptions-item label="Product Code">{{
            selectedRecord.spr_prod_cod
          }}</a-descriptions-item>
          <a-descriptions-item label="Product Name">{{
            selectedRecord.spd_prod_nam
          }}</a-descriptions-item>
          <a-descriptions-item label="RTPC Quantity">{{
            selectedRecord.spr_rtpc_qty
          }}</a-descriptions-item>
          <a-descriptions-item label="PPC Number">{{
            selectedRecord.prod_ppc_num
          }}</a-descriptions-item>
          <a-descriptions-item label="HRC1">{{ selectedRecord.prod_hrc1 }}</a-descriptions-item>
          <a-descriptions-item label="AI">{{ selectedRecord.ai }}</a-descriptions-item>
          <a-descriptions-item label="Inner Master">{{
            selectedRecord.inner_master
          }}</a-descriptions-item>
          <a-descriptions-item label="Carton">{{ selectedRecord.carton }}</a-descriptions-item>
          <a-descriptions-item label="PCS">{{ selectedRecord.pcs }}</a-descriptions-item>
          <a-descriptions-item label="Inner Carton">{{
            selectedRecord.inner_carton
          }}</a-descriptions-item>
          <a-descriptions-item label="Inner PCS">{{
            selectedRecord.inner_pcs
          }}</a-descriptions-item>
          <a-descriptions-item label="Total Carton">{{
            selectedRecord.total_carton
          }}</a-descriptions-item>
          <a-descriptions-item label="Total PCS">{{
            selectedRecord.total_pcs
          }}</a-descriptions-item>
          <a-descriptions-item label="Total AI">{{ selectedRecord.total_ai }}</a-descriptions-item>
          <a-descriptions-item label="Total M3">{{ selectedRecord.total_m3 }}</a-descriptions-item>
          <a-descriptions-item label="Created At" :span="2">{{
            formatDateTime(selectedRecord.created_at)
          }}</a-descriptions-item>
          <a-descriptions-item label="Updated At" :span="2">{{
            formatDateTime(selectedRecord.updated_at)
          }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- Delete Confirmation Modal -->
    <a-modal
      v-model:open="showDeleteModal"
      title="Confirm Delete"
      :width="400"
      @ok="confirmDelete"
      @cancel="cancelDelete"
    >
      <p>Are you sure you want to delete this outbound record?</p>
      <p><strong>SPR AS Number:</strong> {{ recordToDelete?.spr_as_num }}</p>
      <p><strong>Product:</strong> {{ recordToDelete?.spd_prod_nam }}</p>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  ClearOutlined,
  ReloadOutlined,
  DownloadOutlined,
} from '@ant-design/icons-vue'
import { useSQLite } from '@IVC/hooks/useSQLite'
import ActionButtons from '@IVC/components/ActionButtons.vue'

// SQLite hook
const { executeQuery, initialize } = useSQLite()

// Reactive state
const isLoading = ref(false)
const errorMessage = ref('')
const hasSearched = ref(false)
const showDetailModal = ref(false)
const showDeleteModal = ref(false)
const selectedRecord = ref<OutboundRecord | null>(null)
const recordToDelete = ref<OutboundRecord | null>(null)

// Data arrays
const outboundData = ref<OutboundRecord[]>([])
const customers = ref<Customer[]>([])
const divisions = ref<Division[]>([])

// Search filters
const searchFilters = reactive({
  customerId: null as string | null,
  divisionId: null as string | null,
  month: null as string | null,
})

// Pagination
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
})

const paginationConfig = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: (total: number, range: [number, number]) =>
    `${range[0]}-${range[1]} of ${total} items`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
})

// Types
interface OutboundRecord {
  id: number
  customer_id: number
  division_id?: number
  customer_name?: string
  division_name?: string
  spr_as_num: string
  spr_asln_num: number
  spr_assq_num: number
  sph_ship_ymd: string
  sph_dlv_cod: number
  sph_dlv_nam1: string
  spr_prod_cod: number
  spd_prod_nam: string
  spr_rtpc_qty: number
  prod_ppc_num: number
  prod_hrc1: string
  ai: number
  inner_master: number
  carton: number
  pcs: number
  inner_carton: number
  inner_pcs: number
  total_carton: number
  total_pcs: number
  total_ai: number
  total_m3: number
  created_at: string
  updated_at: string
}

interface Customer {
  id: string
  name: string
}

interface Division {
  id: string
  name: string
  customer_id: string
}

interface SelectOption {
  value: string
  label?: string
}

// Computed properties
const filteredDivisions = computed(() => {
  if (!searchFilters.customerId) {
    return divisions.value
  }
  return divisions.value.filter((d) => d.customer_id === searchFilters.customerId)
})

// Filter functions for search
const filterCustomerOption = (input: string, option: SelectOption) => {
  const customer = customers.value.find((c) => c.id === option.value)
  if (!customer) return false
  return customer.name.toLowerCase().includes(input.toLowerCase())
}

const filterDivisionOption = (input: string, option: SelectOption) => {
  const division = divisions.value.find((d) => d.id === option.value)
  if (!division) return false
  return division.name.toLowerCase().includes(input.toLowerCase())
}

// Table columns configuration
const tableColumns = [
  {
    title: 'id',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    sorter: true,
    fixed: 'left' as const,
  },
  {
    title: 'customer_name',
    dataIndex: 'customer_name',
    key: 'customer_name',
    width: 150,
    fixed: 'left' as const,
  },
  {
    title: 'division_name',
    dataIndex: 'division_name',
    key: 'division_name',
    width: 120,
  },
  {
    title: 'spr_as_num',
    dataIndex: 'spr_as_num',
    key: 'spr_as_num',
    width: 150,
  },
  {
    title: 'spr_asln_num',
    dataIndex: 'spr_asln_num',
    key: 'spr_asln_num',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'spr_assq_num',
    dataIndex: 'spr_assq_num',
    key: 'spr_assq_num',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'sph_ship_ymd',
    dataIndex: 'sph_ship_ymd',
    key: 'sph_ship_ymd',
    width: 120,
  },
  {
    title: 'sph_dlv_cod',
    dataIndex: 'sph_dlv_cod',
    key: 'sph_dlv_cod',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'sph_dlv_nam1',
    dataIndex: 'sph_dlv_nam1',
    key: 'sph_dlv_nam1',
    width: 150,
    ellipsis: true,
  },
  {
    title: 'spr_prod_cod',
    dataIndex: 'spr_prod_cod',
    key: 'spr_prod_cod',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'spd_prod_nam',
    dataIndex: 'spd_prod_nam',
    key: 'spd_prod_nam',
    width: 200,
    ellipsis: true,
  },
  {
    title: 'spr_rtpc_qty',
    dataIndex: 'spr_rtpc_qty',
    key: 'spr_rtpc_qty',
    width: 100,
    align: 'right' as const,
  },
  {
    title: 'prod_ppc_num',
    dataIndex: 'prod_ppc_num',
    key: 'prod_ppc_num',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'prod_hrc1',
    dataIndex: 'prod_hrc1',
    key: 'prod_hrc1',
    width: 100,
  },
  {
    title: 'ai',
    dataIndex: 'ai',
    key: 'ai',
    width: 80,
    align: 'right' as const,
  },
  {
    title: 'inner_master',
    dataIndex: 'inner_master',
    key: 'inner_master',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'carton',
    dataIndex: 'carton',
    key: 'carton',
    width: 100,
    align: 'right' as const,
  },
  {
    title: 'pcs',
    dataIndex: 'pcs',
    key: 'pcs',
    width: 100,
    align: 'right' as const,
  },
  {
    title: 'inner_carton',
    dataIndex: 'inner_carton',
    key: 'inner_carton',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'inner_pcs',
    dataIndex: 'inner_pcs',
    key: 'inner_pcs',
    width: 100,
    align: 'right' as const,
  },
  {
    title: 'total_carton',
    dataIndex: 'total_carton',
    key: 'total_carton',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'total_pcs',
    dataIndex: 'total_pcs',
    key: 'total_pcs',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'total_ai',
    dataIndex: 'total_ai',
    key: 'total_ai',
    width: 100,
    align: 'right' as const,
  },
  {
    title: 'total_m3',
    dataIndex: 'total_m3',
    key: 'total_m3',
    width: 100,
    align: 'right' as const,
  },
  {
    title: 'created_at',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 150,
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 150,
    fixed: 'right' as const,
  },
]

// Initialize component
onMounted(async () => {
  await initializeData()
})

// Initialize data
const initializeData = async () => {
  try {
    isLoading.value = true
    await initialize()
    await loadCustomers()
    await loadDivisions()
    await searchData() // Load initial data
  } catch (error) {
    console.error('Error initializing data:', error)
    errorMessage.value = 'Failed to initialize data'
  } finally {
    isLoading.value = false
  }
}

// Load customers for dropdown
const loadCustomers = async () => {
  try {
    const result = await executeQuery('SELECT id, name FROM customers')
    customers.value =
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        name: String(row[1]),
      })) || []
  } catch (error) {
    console.error('Error loading customers:', error)
    message.error('Failed to load customers')
  }
}

// Load divisions for dropdown
const loadDivisions = async (customerId?: string) => {
  try {
    let sql = 'SELECT id, name, customer_id FROM customer_divisions'
    const params: string[] = []

    if (customerId) {
      sql += ' WHERE customer_id = ?'
      params.push(customerId)
    }

    const result = await executeQuery(sql, params)
    divisions.value =
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        name: String(row[1]),
        customer_id: String(row[2]),
      })) || []
  } catch (error) {
    console.error('Error loading divisions:', error)
    message.error('Failed to load divisions')
  }
}

// Search data with filters
const searchData = async () => {
  try {
    isLoading.value = true
    errorMessage.value = ''

    let sql = `
      SELECT 
        co.id,
        co.customer_id,
        co.division_id,
        co.spr_as_num,
        co.spr_asln_num,
        co.spr_assq_num,
        co.sph_ship_ymd,
        co.sph_dlv_cod,
        co.sph_dlv_nam1,
        co.spr_prod_cod,
        co.spd_prod_nam,
        co.spr_rtpc_qty,
        co.prod_ppc_num,
        co.prod_hrc1,
        co.ai,
        co.inner_master,
        co.carton,
        co.pcs,
        co.inner_carton,
        co.inner_pcs,
        co.total_carton,
        co.total_pcs,
        co.total_ai,
        co.total_m3,
        co.created_at,
        co.updated_at,
        c.name as customer_name,
        cd.name as division_name
      FROM customer_outbound co
      LEFT JOIN customers c ON co.customer_id = c.id
      LEFT JOIN customer_divisions cd ON co.division_id = cd.id
      WHERE 1=1
    `
    const params: (string | number)[] = []

    // Add customer filter
    if (searchFilters.customerId) {
      sql += ' AND co.customer_id = ?'
      params.push(searchFilters.customerId)
    }

    // Add division filter
    if (searchFilters.divisionId) {
      sql += ' AND co.division_id = ?'
      params.push(searchFilters.divisionId)
    }

    // Add month filter
    if (searchFilters.month) {
      sql += ` AND strftime('%Y-%m', co.sph_ship_ymd) = ?`
      params.push(searchFilters.month)
    }

    // Add ordering
    sql += ' ORDER BY co.created_at DESC'

    // Add pagination
    const offset = (pagination.current - 1) * pagination.pageSize
    sql += ` LIMIT ? OFFSET ?`
    params.push(pagination.pageSize, offset)

    console.log('Search SQL:', sql)
    console.log('Search params:', params)

    const result = await executeQuery(sql, params)
    const rows = result?.result?.resultRows || []

    // Get total count for pagination
    let countSql = `
      SELECT COUNT(*) as total
      FROM customer_outbound co
      LEFT JOIN customers c ON co.customer_id = c.id
      LEFT JOIN customer_divisions cd ON co.division_id = cd.id
      WHERE 1=1
    `
    const countParams: (string | number)[] = []

    if (searchFilters.customerId) {
      countSql += ' AND co.customer_id = ?'
      countParams.push(searchFilters.customerId)
    }

    if (searchFilters.divisionId) {
      countSql += ' AND co.division_id = ?'
      countParams.push(searchFilters.divisionId)
    }

    if (searchFilters.month) {
      countSql += ` AND strftime('%Y-%m', co.sph_ship_ymd) = ?`
      countParams.push(searchFilters.month)
    }

    const countResult = await executeQuery(countSql, countParams)
    const totalCount = countResult?.result?.resultRows?.[0]?.[0] || 0

    // Map rows to objects
    outboundData.value = rows.map((row: unknown[]) => ({
      id: Number(row[0]),
      customer_id: Number(row[1]),
      division_id: row[2] ? Number(row[2]) : undefined,
      spr_as_num: String(row[3] || ''),
      spr_asln_num: Number(row[4] || 0),
      spr_assq_num: Number(row[5] || 0),
      sph_ship_ymd: String(row[6] || ''),
      sph_dlv_cod: Number(row[7] || 0),
      sph_dlv_nam1: String(row[8] || ''),
      spr_prod_cod: Number(row[9] || 0),
      spd_prod_nam: String(row[10] || ''),
      spr_rtpc_qty: Number(row[11] || 0),
      prod_ppc_num: Number(row[12] || 0),
      prod_hrc1: String(row[13] || ''),
      ai: Number(row[14] || 0),
      inner_master: Number(row[15] || 0),
      carton: Number(row[16] || 0),
      pcs: Number(row[17] || 0),
      inner_carton: Number(row[18] || 0),
      inner_pcs: Number(row[19] || 0),
      total_carton: Number(row[20] || 0),
      total_pcs: Number(row[21] || 0),
      total_ai: Number(row[22] || 0),
      total_m3: Number(row[23] || 0),
      created_at: String(row[24] || ''),
      updated_at: String(row[25] || ''),
      customer_name: String(row[26] || ''),
      division_name: String(row[27] || ''),
    }))

    pagination.total = Number(totalCount)
    paginationConfig.total = Number(totalCount)
    paginationConfig.current = pagination.current

    hasSearched.value = true

    if (outboundData.value.length === 0 && hasSearched.value) {
      message.info('No records found matching your criteria')
    }
  } catch (error) {
    console.error('Error searching data:', error)
    errorMessage.value =
      'Failed to search data: ' + (error instanceof Error ? error.message : String(error))
    message.error('Failed to search data')
  } finally {
    isLoading.value = false
  }
}

// Event handlers
const handleCustomerChange = async () => {
  // Clear division when customer changes
  searchFilters.divisionId = null

  // Load divisions for selected customer
  if (searchFilters.customerId) {
    await loadDivisions(searchFilters.customerId)
  } else {
    await loadDivisions()
  }

  searchData()
}

const handleDivisionChange = () => {
  searchData()
}

const handleMonthChange = () => {
  searchData()
}

const clearFilters = async () => {
  searchFilters.customerId = null
  searchFilters.divisionId = null
  searchFilters.month = null
  pagination.current = 1
  paginationConfig.current = 1
  await loadDivisions() // Load all divisions when clearing filters
  await searchData()
}

const refreshData = () => {
  searchData()
}

// Define types for table change event
interface TableChangeParams {
  current?: number
  pageSize?: number
  total?: number
}

const handleTableChange = (pag: TableChangeParams) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 20
  paginationConfig.current = pag.current || 1
  paginationConfig.pageSize = pag.pageSize || 20
  searchData()
}

// View record details
const viewRecord = (record: OutboundRecord) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

// Delete record
const deleteRecord = (record: OutboundRecord) => {
  recordToDelete.value = record
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  if (!recordToDelete.value) return

  try {
    isLoading.value = true
    await executeQuery('DELETE FROM customer_outbound WHERE id = ?', [recordToDelete.value.id])
    message.success('Record deleted successfully')
    showDeleteModal.value = false
    recordToDelete.value = null
    await searchData() // Refresh data
  } catch (error) {
    console.error('Error deleting record:', error)
    message.error('Failed to delete record')
  } finally {
    isLoading.value = false
  }
}

const cancelDelete = () => {
  showDeleteModal.value = false
  recordToDelete.value = null
}

// Export data
const exportData = () => {
  // Implementation for export functionality
  message.info('Export functionality will be implemented')
}

// Define type for select option
interface SelectOption {
  value: string
  label?: string
}

// Utility functions
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
  } catch {
    return dateString
  }
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return dateString
  }
}
</script>

<style scoped>
.outbound-list-container {
  padding: 24px;
  background-color: #ffffff;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  color: #1a1a1a;
}

.page-header p {
  margin: 4px 0 0 0;
  color: #666;
}

.search-section {
  margin-bottom: 24px;
}

.search-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-summary {
  margin-bottom: 16px;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.record-detail .ant-descriptions {
  margin-top: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .outbound-list-container {
    padding: 16px;
  }

  .search-section .ant-col {
    margin-bottom: 16px;
  }
}
</style>
