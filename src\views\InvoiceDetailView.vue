<template>
  <div class="invoice-form-view">
    <div class="sticky-header">
      <div class="header-content">
        <div class="header-left">
          <a-tag :color="getStatusColor(invoiceForm.status)">
            {{ getStatusText(invoiceForm.status) }}
          </a-tag>
          <h2>{{ getPageTitle }}</h2>
        </div>
        <div class="header-right">
          <span class="grand-total">Grand Total: {{ formatCurrency(grandTotal) }}</span>
          <div class="action-buttons">
            <a-button @click="handleCancel">Cancel</a-button>
            <a-button v-if="canSave" type="primary" @click="handleSave"> Save </a-button>
            <a-button v-if="canApprove" type="primary" @click="handleApprove"> Approve </a-button>
            <a-button v-if="canDisapprove" @click="handleDisapprove"> Disapprove </a-button>
          </div>
        </div>
      </div>
    </div>

    <div class="form-content">
      <div class="invoice-form">
        <div class="form-row">
          <div class="form-col">
            <a-form-item label="Customer" class="compact-form-item">
              <a-select
                v-model:value="invoiceForm.customerId"
                :disabled="isReadOnly"
                :options="customers.map((c) => ({ value: c.id, label: c.name }))"
              />
            </a-form-item>
          </div>
          <div class="form-col">
            <a-form-item label="Division" class="compact-form-item">
              <a-select
                v-model:value="invoiceForm.customerDivisionId"
                :disabled="isReadOnly"
                :options="divisions.map((d) => ({ value: d.id, label: d.name }))"
              />
            </a-form-item>
          </div>
          <div class="form-col form-col-notes">
            <a-form-item label="Notes" class="compact-form-item">
              <a-textarea
                v-model:value="invoiceForm.notes"
                :rows="1"
                :disabled="isReadOnly"
                :auto-size="{ minRows: 1, maxRows: 2 }"
              />
            </a-form-item>
          </div>
        </div>
      </div>

      <div class="categories-section">
        <div class="section-header">
          <h3>Categories</h3>
          <a-button
            v-if="!isReadOnly"
            type="primary"
            @click="showCategoryDialog"
            :disabled="!hasAvailableCategories"
          >
            <template #icon><PlusOutlined /></template>
            Add Category
          </a-button>
        </div>

        <invoice-category-list
          :categories="invoiceForm.categories"
          :all-categories="allCategories"
          :workouts="workouts"
          :units="units"
          :customer-id="invoiceForm.customerId"
          :customer-division-id="invoiceForm.customerDivisionId"
          :readonly="isReadOnly"
          @add-charge="handleAddCharge"
          @update-charge="handleUpdateCharge"
          @delete-charge="handleDeleteCharge"
          @delete-category="handleDeleteCategory"
        />

        <div v-if="invoiceForm.categories.length === 0" class="empty-state">
          <p>No categories added yet. {{ isReadOnly ? '' : 'Click "Add Category" to start.' }}</p>
        </div>
      </div>
    </div>

    <category-select-dialog
      :visible="categoryDialogVisible"
      :all-categories="allCategories"
      :current-categories="invoiceForm.categories"
      @update:visible="categoryDialogVisible = $event"
      @select="handleCategorySelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { PlusOutlined } from '@ant-design/icons-vue'
import { useInvoiceService } from '../services/invoiceLocalService'
import InvoiceCategoryList from '../components/InvoiceCategoryList.vue'
import CategorySelectDialog from '../components/dialogs/CategorySelectDialog.vue'
import type { Invoice, InvoiceCategory, InvoiceCharge } from '../types/invoice'
import type { Category } from '../types/MasterDataTypes/Category'
import type { Customer } from '../types/MasterDataTypes/Customer'
import type { Unit } from '../types/MasterDataTypes/Unit'
import type { Workout } from '../types/MasterDataTypes/Category'
import type { CustomerDivision } from '../types/MasterDataTypes/Customer'
import dayjs from '../plugins/dayjs'
import { message } from 'ant-design-vue'

const route = useRoute()
const router = useRouter()
const invoiceService = useInvoiceService()

const mode = computed(() => {
  if (route.query.mode === 'view') return 'view'
  return isEditMode.value ? 'edit' : 'new'
})

const isEditMode = computed(() => !!route.query.id)
const invoiceId = computed(() => route.query.id as string | undefined)

const customers = ref<Customer[]>([])
const divisions = ref<CustomerDivision[]>([])
const allCategories = ref<Category[]>([])
const workouts = ref<Workout[]>([])
const units = ref<Unit[]>([])
const categoryDialogVisible = ref(false)

const invoiceForm = reactive<Invoice>({
  id: '',
  customerId: undefined,
  customerDivisionId: undefined,
  totalAmount: 0,
  status: 'DRAFT',
  notes: '',
  createdAt: '',
  updatedAt: '',
  categories: [],
})

// Add watch for customer changes
watch(
  () => invoiceForm.customerId,
  async (newCustomerId) => {
    invoiceForm.customerDivisionId = undefined
    divisions.value = []

    if (newCustomerId) {
      try {
        divisions.value = await invoiceService.getCustomerDivisions(newCustomerId)
      } catch (error) {
        console.error('Error fetching divisions:', error)
        message.error('Failed to load divisions')
      }
    }
  },
)

const hasAvailableCategories = computed(() => {
  return allCategories.value.some(
    (cat: Category) => !invoiceForm.categories.some((c) => c.categoryId === cat.id),
  )
})

const getPageTitle = computed(() => {
  switch (mode.value) {
    case 'view':
      return 'View Invoice'
    case 'edit':
      return 'Edit Invoice'
    default:
      return 'New Invoice'
  }
})

const isReadOnly = computed(() => {
  if (mode.value === 'view') return true
  if (!isEditMode.value) return false

  // In edit mode, check status
  return ['APPROVED', 'PAID', 'OVERDUE'].includes(invoiceForm.status)
})

const canSave = computed(() => {
  if (mode.value === 'view') return false
  if (!isEditMode.value) return true

  // In edit mode, check status
  return ['DRAFT', 'PENDING', 'REJECTED'].includes(invoiceForm.status)
})

const canApprove = computed(() => {
  if (!isEditMode.value || mode.value === 'view') return false

  // Can approve if status is DRAFT, PENDING, or REJECTED
  return ['DRAFT', 'PENDING', 'REJECTED'].includes(invoiceForm.status)
})

const canDisapprove = computed(() => {
  if (!isEditMode.value) return false

  // Can only disapprove if status is APPROVED
  return invoiceForm.status === 'APPROVED'
})

const formatCurrency = (amount: number | undefined) => {
  if (amount === undefined) return '$0.00'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'DRAFT':
      return 'processing'
    case 'PENDING':
      return 'warning'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'error'
    case 'PAID':
      return 'success'
    case 'OVERDUE':
      return 'error'
    default:
      return undefined
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'DRAFT':
      return 'Draft'
    case 'PENDING':
      return 'Pending'
    case 'APPROVED':
      return 'Approved'
    case 'REJECTED':
      return 'Rejected'
    case 'PAID':
      return 'Paid'
    case 'OVERDUE':
      return 'Overdue'
    default:
      return status
  }
}

const grandTotal = computed(() => {
  return invoiceForm.categories.reduce(
    (sum: number, cat: InvoiceCategory) =>
      sum + cat.charges.reduce((s: number, c: InvoiceCharge) => s + (c.totalAmount || 0), 0),
    0,
  )
})

const fetchMasterData = async () => {
  customers.value = await invoiceService.getAllCustomers()
  allCategories.value = await invoiceService.getAllCategories()
  workouts.value = await invoiceService.getAllWorkouts()
  units.value = await invoiceService.getAllUnits()

  // If we have a customer ID, fetch its divisions
  if (invoiceForm.customerId) {
    try {
      divisions.value = await invoiceService.getCustomerDivisions(invoiceForm.customerId)
    } catch (error) {
      console.error('Error fetching divisions:', error)
      message.error('Failed to load divisions')
    }
  }
}

const fetchInvoice = async () => {
  if (isEditMode.value && invoiceId.value) {
    const invoice = (await invoiceService.getAllInvoices()).find((i) => i.id === invoiceId.value)
    if (invoice) {
      Object.assign(invoiceForm, invoice)
    }
  }
}

const showCategoryDialog = () => {
  categoryDialogVisible.value = true
}

const handleCategorySelect = (categoryIds: number[]) => {
  categoryIds.forEach((categoryId) => {
    const category = allCategories.value.find((c: Category) => c.id === categoryId)
    if (category) {
      invoiceForm.categories.push({
        id: `CAT-${Date.now()}-${categoryId}`,
        invoiceId: invoiceForm.id,
        categoryId: category.id,
        name: category.name,
        order: invoiceForm.categories.length + 1,
        charges: [],
      })
    }
  })
}

const handleDeleteCategory = (categoryId: string) => {
  invoiceForm.categories = invoiceForm.categories.filter((c) => c.id !== categoryId)
}

const handleAddCharge = (categoryId: string) => {
  const cat = invoiceForm.categories.find((c: InvoiceCategory) => c.id === categoryId)
  if (cat) {
    cat.charges.push({
      id: `CHG-${Date.now()}`,
      invoiceId: invoiceForm.id,
      categoryId: cat.categoryId,
      workoutId: undefined,
      unitId: undefined,
      unitPrice: 0,
      quantity: 0,
      totalAmount: 0,
      status: 'DRAFT',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    })
  }
}

const handleUpdateCharge = (categoryId: string, charge: InvoiceCharge) => {
  const cat = invoiceForm.categories.find((c: InvoiceCategory) => c.id === categoryId)
  if (cat) {
    const idx = cat.charges.findIndex((ch: InvoiceCharge) => ch.id === charge.id)
    if (idx !== -1) cat.charges[idx] = { ...charge }
  }
}

const handleDeleteCharge = (categoryId: string, chargeId: string) => {
  const cat = invoiceForm.categories.find((c: InvoiceCategory) => c.id === categoryId)
  if (cat) {
    cat.charges = cat.charges.filter((ch: InvoiceCharge) => ch.id !== chargeId)
  }
}

const handleSave = async () => {
  try {
    if (isReadOnly.value) return

    invoiceForm.totalAmount = grandTotal.value
    invoiceForm.updatedAt = new Date().toISOString()

    if (isEditMode.value) {
      await invoiceService.updateInvoice(invoiceForm.id, { ...invoiceForm })
    } else {
      invoiceForm.id = `INV-${Date.now()}`
      invoiceForm.createdAt = new Date().toISOString()
      invoiceForm.updatedAt = invoiceForm.createdAt
      await invoiceService.createInvoice({ ...invoiceForm })
    }

    message.success('Invoice saved successfully')
    router.push({ name: 'Invoice List' })
  } catch (error) {
    message.error('Failed to save invoice')
    console.error('Error saving invoice:', error)
  }
}

const handleCancel = () => {
  router.push({ name: 'Invoice List' })
}

const handleApprove = async () => {
  try {
    if (!canApprove.value) return

    invoiceForm.status = 'APPROVED'
    await invoiceService.updateInvoice(invoiceForm.id, { status: 'APPROVED' })
    message.success('Invoice approved successfully')
    router.push({ name: 'Invoice List' })
  } catch (error) {
    message.error('Failed to approve invoice')
    console.error('Error approving invoice:', error)
  }
}

const handleDisapprove = async () => {
  try {
    if (!canDisapprove.value) return

    invoiceForm.status = 'DRAFT'
    await invoiceService.updateInvoice(invoiceForm.id, { status: 'DRAFT' })
    message.success('Invoice disapproved successfully')
    router.push({ name: 'Invoice List' })
  } catch (error) {
    message.error('Failed to disapprove invoice')
    console.error('Error disapproving invoice:', error)
  }
}

onMounted(async () => {
  await fetchMasterData()
  await fetchInvoice()

  // For new invoice, check if customer and division are provided in query params
  if (!isEditMode.value) {
    const customerId = route.query.customerId ? Number(route.query.customerId) : undefined
    if (customerId) {
      invoiceForm.customerId = customerId
      // Divisions will be fetched by the watcher

      // If division is provided and customer exists, set it after divisions are loaded
      const divisionId = route.query.divisionId ? Number(route.query.divisionId) : undefined
      if (divisionId) {
        // Wait for divisions to be loaded
        await new Promise((resolve) => setTimeout(resolve, 100))
        if (divisions.value.find((d) => d.id === divisionId)) {
          invoiceForm.customerDivisionId = divisionId
        }
      }
    }
  }
})
</script>

<style scoped>
.invoice-form-view {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

.grand-total {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.form-content {
  flex: 1;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.invoice-form {
  background: #fff;
  padding: 16px 24px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.form-row {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.form-col {
  flex: 0 0 250px;
}

.form-col-notes {
  flex: 1;
  width: 100%;
}

:deep(.compact-form-item) {
  margin-bottom: 0;

  .ant-form-item-label {
    padding-bottom: 4px;
  }

  .ant-select {
    width: 100%;
  }
}

.categories-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.section-header h3 {
  margin: 0;
  color: #262626;
  font-size: 18px;
}

.empty-state {
  text-align: center;
  padding: 48px;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  color: #8c8c8c;
}
</style>
