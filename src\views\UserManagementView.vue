<template>
  <div class="user-management">
    <a-page-header title="User Management" sub-title="Manage your user details">
      <template #extra>
        <a-button type="primary" @click="showCreateModal"> <PlusOutlined /> Add New User </a-button>
      </template>
    </a-page-header>

    <!-- Users Table -->
    <a-table :columns="columns" :data-source="users" :loading="loading || isLoading" row-key="id">
      <!-- Name Column -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <a @click="showEditModal(record)" class="user-name-link">
            {{ record.name }}
          </a>
        </template>

        <!-- Role Column -->
        <template v-else-if="column.key === 'role'">
          <a-tag :color="getRoleColor(record.role)" class="role-tag">
            {{ record.role.toUpperCase() }}
          </a-tag>
        </template>

        <!-- Status Column -->
        <template v-else-if="column.key === 'status'">
          <a-switch
            v-model:checked="record.status"
            @change="handleStatusChange(record)"
            :checked-children="'Active'"
            :un-checked-children="'Inactive'"
            class="status-switch"
          />
        </template>

        <!-- Actions Column -->
        <template v-else-if="column.key === 'actions'">
          <ActionButtons
            name="User"
            :show-save="false"
            :show-edit="true"
            :show-delete="true"
            :show-view="false"
            @edit="showEditModal(record)"
            @delete="showDeleteConfirm(record)"
          />
        </template>
      </template>
    </a-table>

    <!-- Create/Edit User Modal -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEditing ? 'Edit User' : 'Create New User'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitting"
    >
      <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
        <a-form-item label="Name" name="name">
          <a-input v-model:value="formData.name" placeholder="Enter full name" />
        </a-form-item>

        <a-form-item label="Username" name="username">
          <a-input v-model:value="formData.username" placeholder="Enter username" />
        </a-form-item>

        <a-form-item label="Password" name="password">
          <a-input-password v-model:value="formData.password" placeholder="Enter password" />
        </a-form-item>

        <a-form-item label="Email" name="email">
          <a-input v-model:value="formData.email" placeholder="Enter email address" type="email" />
        </a-form-item>

        <a-form-item label="Role" name="role">
          <a-select
            v-model:value="formData.role"
            placeholder="Select role"
            :options="roleOptions"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="Status" name="status">
          <a-switch
            v-model:checked="formData.status"
            :checked-children="'Active'"
            :un-checked-children="'Inactive'"
          />
          <span style="margin-left: 8px; color: #666">
            {{ formData.status ? 'User is active' : 'User is inactive' }}
          </span>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { Modal } from 'ant-design-vue'
import type { TableColumnsType } from 'ant-design-vue'
import { useSQLite } from '../hooks/useSQLite'
import { type User } from '../types/MasterDataTypes/User'
import ActionButtons from '../components/ActionButtons.vue'
import {
  getAllUsers,
  addUser,
  updateUser,
  deleteUser as deleteUserService,
  updateUserStatus,
} from '../services/master-data/user/index'

const { isLoading } = useSQLite()

// Reactive data
const loading = ref(false)
const modalVisible = ref(false)
const isEditing = ref(false)
const submitting = ref(false)
const formRef = ref()

// Users data
const users = ref<User[]>([])

// Form data
const formData = reactive({
  id: undefined as number | undefined,
  username: '',
  password: '',
  email: '',
  role: 'user' as 'admin' | 'manager' | 'user',
  name: '',
  status: true,
})

// Table columns
const columns: TableColumnsType = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    sorter: (a: User, b: User) => a.name.localeCompare(b.name),
  },
  {
    title: 'Username',
    dataIndex: 'username',
    key: 'username',
    sorter: (a: User, b: User) => a.username.localeCompare(b.username),
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
  },
  {
    title: 'Role',
    key: 'role',
    dataIndex: 'role',
  },
  {
    title: 'Status',
    key: 'status',
    dataIndex: 'status',
    width: 120,
  },
  {
    title: 'Action',
    key: 'actions',
    width: 100,
    align: 'center',
  },
]

// Pagination - simplified since we're not using pagination with SQLite
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `${range[0]}-${range[1]} of ${total} items`,
})

// Role options
const roleOptions = [
  { label: 'User', value: 'user' },
  { label: 'Manager', value: 'manager' },
  { label: 'Admin', value: 'admin' },
]

// Form validation rules
const formRules = {
  name: [
    { required: true, message: 'Please enter full name', trigger: 'blur' },
    { min: 2, message: 'Name must be at least 2 characters', trigger: 'blur' },
  ],
  username: [
    { required: true, message: 'Please enter username', trigger: 'blur' },
    { min: 3, message: 'Username must be at least 3 characters', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9._]+$/,
      message: 'Username can only contain letters, numbers, dots and underscores',
      trigger: 'blur',
    },
  ],
  password: [
    { required: true, message: 'Please enter password', trigger: 'blur' },
    { min: 6, message: 'Password must be at least 6 characters', trigger: 'blur' },
  ],
  email: [
    { required: true, message: 'Please enter email address', trigger: 'blur' },
    { type: 'email', message: 'Please enter a valid email address', trigger: 'blur' },
  ],
  role: [{ required: true, message: 'Please select a role', trigger: 'change' }],
}

// Methods
const getRoleColor = (role: string): string => {
  const colors: Record<string, string> = {
    admin: 'red',
    manager: 'orange',
    user: 'blue',
  }
  return colors[role.toLowerCase()] || 'default'
}

// Load users from service
const loadUsers = async () => {
  try {
    loading.value = true
    const allUsers = await getAllUsers()
    users.value = allUsers
    pagination.total = allUsers.length
  } catch (error) {
    console.error('Error loading users:', error)
  } finally {
    loading.value = false
  }
}

const showCreateModal = () => {
  isEditing.value = false
  modalVisible.value = true
  resetForm()
}

const showEditModal = (user: User) => {
  isEditing.value = true
  modalVisible.value = true
  Object.assign(formData, user)
}

const resetForm = () => {
  Object.assign(formData, {
    id: undefined,
    username: '',
    password: '',
    email: '',
    role: 'user',
    name: '',
    status: true,
  })
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEditing.value) {
      // Update existing user
      await updateUser(formData)
    } else {
      // Create new user
      await addUser(formData)
    }

    modalVisible.value = false
    resetForm()
    await loadUsers() // Reload users list
  } catch (error) {
    console.error('Form submission failed:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

const handleStatusChange = async (user: User) => {
  try {
    await updateUserStatus(user.id, user.status)
  } catch {
    // Revert status on error
    user.status = !user.status
  }
}

const showDeleteConfirm = (user: User) => {
  Modal.confirm({
    title: 'Are you sure you want to delete this user?',
    content: `This will permanently delete ${user.name}'s account.`,
    okText: 'Delete',
    okType: 'danger',
    cancelText: 'Cancel',
    onOk() {
      handleDeleteUser(user.id)
    },
  })
}

const handleDeleteUser = async (userId: number) => {
  try {
    await deleteUserService(userId)
    await loadUsers() // Reload users list
  } catch (error) {
    console.error('Error deleting user:', error)
  }
}

onMounted(() => {
  // Load users when component is mounted
  loadUsers()
})
</script>

<style scoped></style>
