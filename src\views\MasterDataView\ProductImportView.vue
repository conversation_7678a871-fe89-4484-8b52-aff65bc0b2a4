<template>
  <div class="import-container-product">
    <PageHeader
      title="Import Products"
      sub-title="Import product data from Excel files"
      @back="() => $router.push({ name: 'product' })"
    />

    <div style="padding: 0 16px">
      <!-- Customer Selection Section (like Search Filters) -->
      <div class="search-section-product">
        <ACard :bordered="false" class="search-card-product">
          <AForm layout="vertical">
            <AForm layout="vertical">
              <AFormItem label="Select Customer" required>
                <Select
                  v-model:value="selectedCustomerId"
                  placeholder="Choose a customer"
                  style="width: 100%"
                  @change="handleCustomerChange"
                  show-search
                  option-filter-prop="label"
                  :loading="customers.length === 0 && isCustomersLoading"
                  :disabled="isImporting"
                >
                  <Select.Option
                    v-for="customer in customers"
                    :key="customer.id"
                    :value="customer.id"
                    :label="customer.name"
                  >
                    {{ customer.name }} ({{ customer.code }})
                  </Select.Option>
                </Select>
              </AFormItem>
            </AForm>
          </AForm>
        </ACard>
      </div>

      <!-- Alerts for error/success (can be added here if needed, similar to ImportInbound) -->
      <!-- For now, using Ant Design message for feedback -->

      <!-- File Upload Section -->
      <div class="file-upload-product">
        <div>
          <input
            ref="fileInputRef"
            type="file"
            accept=".xlsx,.xls"
            @change="handleFileSelect"
            style="display: none"
          />
          <div
            v-if="!fileSelected"
            class="drop-zone-product"
            :class="{ active: isDraggingOverDropZone }"
            @dragenter.prevent="isDraggingOverDropZone = true"
            @dragover.prevent="isDraggingOverDropZone = true"
            @dragleave.prevent="isDraggingOverDropZone = false"
            @drop.prevent="handleFileDrop"
            @click="() => fileInputRef?.click()"
            :style="{
              opacity: !selectedCustomerId || isImporting ? 0.5 : 1,
              cursor: !selectedCustomerId || isImporting ? 'not-allowed' : 'pointer',
            }"
          >
            <div class="drop-message-product">
              <div class="upload-icon-product">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
              </div>
              <p style="margin-top: 8px">Drag & drop Excel file here, or click to select.</p>
              <small v-if="!selectedCustomerId">(Please select a customer first)</small>
            </div>
          </div>

          <div v-if="fileSelected && isFileProcessed" class="file-metadata-display-product">
            <h3>File Details:</h3>
            <p><strong>Name:</strong> {{ fileSelected.name }}</p>
            <p v-if="isFileProcessed && !isPreviewLoading">
              <strong>Products Found:</strong> {{ totalProductsToImport }}
            </p>
            <p v-if="isFileProcessed && !isPreviewLoading && rawExcelHeadersFromFile.length > 0">
              <strong>Excel Columns:</strong> {{ previewTableColumns.length }}
            </p>
          </div>
        </div>

        <div class="form-actions-product">
          <AButton
            @click="handleRemoveFile"
            :disabled="!fileSelected || isPreviewLoading || isImporting"
          >
            Clear File
          </AButton>
          <AButton
            type="primary"
            @click="openImportConfirmationModal"
            :loading="isImporting"
            :disabled="
              !selectedCustomerId ||
              isPreviewLoading ||
              !isFileProcessed ||
              totalProductsToImport === 0 ||
              isImporting ||
              !fileSelected
            "
            size="middle"
          >
            {{ isImporting ? 'Importing...' : 'Start Import Process' }}
          </AButton>
        </div>
      </div>

      <!-- Column Mapping Section -->
      <div
        v-if="isFileProcessed && !isPreviewLoading && fileSelected"
        class="mapping-container-product"
      >
        <ARow :gutter="16" style="margin-bottom: 16px">
          <ACol :span="12"><h3>Column Mapping</h3></ACol>
          <ACol :span="12" style="text-align: right">
            <ASpace>
              <AButton @click="clearAllUiMappings" style="margin-right: 8px"
                >Clear All Mappings</AButton
              >
              <AButton
                type="primary"
                @click="openSaveMappingModal"
                :disabled="Object.keys(uiColumnMapping).length === 0"
                >Save Current Mapping</AButton
              >
            </ASpace>
          </ACol>
        </ARow>
        <p class="mapping-instructions">
          Map your Excel columns (left) to the corresponding Database Fields (right). Click an Excel
          column, then click a Database Field to create a link.
        </p>
        <div class="mapping-interface">
          <div class="excel-columns-map-view">
            <h4>Excel Columns (from your file)</h4>
            <div class="column-list">
              <div
                v-for="excelHeaderTitle in rawExcelHeadersFromFile"
                :key="excelHeaderTitle"
                class="column-item excel-column"
                :class="{
                  active: selectedExcelColumnForMapping === excelHeaderTitle,
                  mapped: uiColumnMapping[excelHeaderTitle],
                }"
                @click="selectExcelColumnForMapping(excelHeaderTitle)"
              >
                {{ excelHeaderTitle }}
                <span v-if="uiColumnMapping[excelHeaderTitle]" class="mapped-indicator">
                  →
                  {{
                    databaseProductFields.find((f) => f.key === uiColumnMapping[excelHeaderTitle])
                      ?.label || uiColumnMapping[excelHeaderTitle]
                  }}
                  <CloseCircleFilled
                    @click.stop="clearUiMappingForExcelColumn(excelHeaderTitle)"
                    class="clear-map-icon"
                  />
                </span>
              </div>
            </div>
          </div>
          <div class="db-columns-map-view">
            <h4>Database Fields (Product)</h4>
            <div class="column-list">
              <div
                v-for="dbField in databaseProductFields"
                :key="dbField.key"
                class="column-item db-column"
                :class="{
                  mapped: Object.values(uiColumnMapping).includes(dbField.key),
                  disabled:
                    Object.values(uiColumnMapping).includes(dbField.key) &&
                    selectedExcelColumnForMapping &&
                    uiColumnMapping[selectedExcelColumnForMapping] !== dbField.key,
                }"
                @click="mapSelectedExcelColumnToDbField(dbField.key)"
              >
                {{ dbField.label }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Preview & Action Section -->
      <div v-if="fileSelected" class="data-table-container-product">
        <div v-if="isPreviewLoading && fileSelected" style="text-align: center; padding: 20px">
          <Spin tip="Processing file for preview..." size="large" />
        </div>

        <div v-if="!isPreviewLoading && isFileProcessed && fileSelected">
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 16px;
              padding-top: 8px;
            "
          >
            <h3 style="margin: 0">
              Data Preview
              <span v-if="totalProductsToImport > 0">
                ({{ totalProductsToImport }} products found)</span
              >
            </h3>
            <AButton
              v-if="totalProductsToImport > 0"
              type="default"
              @click="openChangesModal"
              :disabled="isPreviewLoading"
            >
              Show Changes
            </AButton>
          </div>
        </div>

        <div v-if="totalProductsToImport > 0 && productSamplesForPreview.length > 0">
          <ATable
            size="small"
            :columns="previewTableColumns"
            :row-class-name="
              (record: ProductForPreview) => (record.hasDifferences ? 'row-has-differences' : '')
            "
            :data-source="productSamplesForPreview"
            row-key="productCode"
            :pagination="{ pageSize: 10, size: 'small' }"
            :scroll="{ x: 800 }"
          >
            <template #bodyCell="{ column, record }">
              <template
                v-if="
                  record.hasDifferences &&
                  previewTableColumns.find(
                    (c) =>
                      c.dataIndex === column.dataIndex &&
                      record.differencesDetail?.includes(c.title),
                  )
                "
              >
                <span style="color: red; font-weight: bold">{{
                  record[column.dataIndex as keyof ProductForPreview]
                }}</span>
              </template>
              <template v-else-if="record.hasDifferences && column.dataIndex === 'productCode'">
                <span style="color: red; font-weight: bold">{{
                  record[column.dataIndex as keyof ProductForPreview]
                }}</span>
              </template>
            </template>
          </ATable>
        </div>

        <Alert
          v-if="
            totalProductsToImport === 0 &&
            fileSelected &&
            rawExcelHeadersFromFile.length > 0 &&
            !isPreviewLoading &&
            isFileProcessed
          "
          message="No products could be mapped from the file. Please check the file content, column mapping for the customer, and ensure product codes are present."
          type="warning"
          show-icon
          style="margin-top: 8px"
        />

        <div
          v-if="!fileSelected && !isPreviewLoading"
          style="text-align: center; padding: 20px; color: #888"
        >
          <AEmpty
            description="Please select a customer and an Excel file to start the import process."
          />
        </div>

        <div
          v-if="!isFileProcessed && !isPreviewLoading && fileSelected"
          style="text-align: center; padding: 20px; color: #888"
        >
          <p>Select a customer and a file to see the preview.</p>
        </div>
      </div>
    </div>

    <!-- Data Difference Warning Modal -->
    <Modal
      v-model:visible="showDataDifferenceWarningModal"
      title="Data Discrepancy Warning"
      :width="600"
    >
      <div style="display: flex; align-items: center; gap: 16px">
        <WarningOutlined style="font-size: 32px; color: #faad14" />
        <p style="margin-bottom: 0">
          Some products in the uploaded file have data that differs from the existing records in the
          database. These are highlighted in red in the preview table.
        </p>
      </div>
      <p>Please review these differences carefully before proceeding with the import.</p>
      <template #footer>
        <AButton key="acknowledge" type="primary" @click="showDataDifferenceWarningModal = false">
          Acknowledge
        </AButton>
      </template>
    </Modal>

    <!-- Confirm Import Modal -->
    <Modal
      v-model:visible="showConfirmImportModal"
      title="Confirm Product Import"
      @cancel="showConfirmImportModal = false"
      :confirm-loading="isImporting"
      width="600px"
    >
      <p>
        Are you sure you want to import <strong>{{ totalProductsToImport }}</strong> products for
        customer
        <strong>{{
          customers.find((c) => c.id === selectedCustomerId)?.name || 'Selected Customer'
        }}</strong
        >?
      </p>
      <div v-if="Object.keys(uiColumnMapping).length > 0">
        <h4>Mapping Summary:</h4>
        <ul>
          <li v-for="(dbFieldKey, excelColTitle) in uiColumnMapping" :key="excelColTitle">
            Excel Column "<strong>{{ excelColTitle }}</strong
            >" will be imported as "<strong>{{
              databaseProductFields.find((f) => f.key === dbFieldKey)?.label || dbFieldKey
            }}</strong
            >".
          </li>
        </ul>
      </div>
      <p v-else>
        No specific column mappings applied (will rely on default or previously saved settings for
        parsing if any).
      </p>
      <template #footer>
        <AButton key="cancel" @click="showConfirmImportModal = false">Cancel</AButton>
        <AButton
          key="continue"
          type="default"
          :loading="isImporting"
          @click="() => executeImportProcessConfirmed(false)"
        >
          Continue Importing
        </AButton>
        <AButton
          key="importAndRedirect"
          type="primary"
          :loading="isImporting"
          @click="() => executeImportProcessConfirmed(true)"
        >
          Import & View Products
        </AButton>
      </template>
    </Modal>

    <!-- Save Mapping Modal -->
    <Modal
      v-model:visible="showSaveMappingModal"
      title="Save Column Mapping"
      @ok="saveCurrentUiMapping"
      @cancel="showSaveMappingModal = false"
      ok-text="Save Mapping"
      cancel-text="Cancel"
      :confirm-loading="isSavingMapping"
    >
      <p>
        Do you want to save the current column mapping for customer
        <strong>{{
          customers.find((c) => c.id === selectedCustomerId)?.name || 'Selected Customer'
        }}</strong
        >?
      </p>
      <p>This will overwrite any previously saved mapping for product import for this customer.</p>
      <div v-if="Object.keys(uiColumnMapping).length > 0" style="margin-top: 16px">
        <p><strong>Current Mappings to be Saved:</strong></p>
        <pre>{{ uiColumnMapping }}</pre>
      </div>
    </Modal>

    <!-- Pieces Per Carton Warning Modal -->
    <Modal
      v-model:visible="showPpcWarningModal"
      title="Pieces Per Carton Changes Detected"
      width="800px"
      @cancel="showPpcWarningModal = false"
    >
      <div style="margin-bottom: 16px">
        <Alert type="warning" show-icon style="margin-bottom: 16px">
          <template #message>Warning: Pieces Per Carton Changes</template>
          <template #description>
            The following products have changes to their "Pieces Per Carton" values. Please review
            these changes carefully before proceeding with the import.
          </template>
        </Alert>

        <div v-if="changesData?.updatedProducts.length">
          <h4>
            Products with Pieces Per Carton Changes ({{ changesData.updatedProducts.length }}):
          </h4>
          <ATable
            size="small"
            :columns="[
              { title: 'Product Code', dataIndex: 'productCode', key: 'productCode', width: 150 },
              { title: 'Product Name', dataIndex: 'productName', key: 'productName', width: 200 },
              { title: 'Old PPC', dataIndex: 'oldPpc', key: 'oldPpc', width: 100 },
              { title: 'New PPC', dataIndex: 'newPpc', key: 'newPpc', width: 100 },
            ]"
            :data-source="
              changesData.updatedProducts.map((product) => ({
                productCode: product.productCode,
                productName: product.productName,
                oldPpc: product.changes?.[0]?.oldValue || '(empty)',
                newPpc: product.changes?.[0]?.newValue || '(empty)',
              }))
            "
            row-key="productCode"
            :pagination="{ pageSize: 5, size: 'small' }"
          />
        </div>
      </div>

      <template #footer>
        <AButton key="cancel" @click="showPpcWarningModal = false"> Cancel Import </AButton>
        <AButton key="proceed" type="primary" @click="proceedWithImport">
          Continue with Import
        </AButton>
      </template>
    </Modal>

    <!-- Changes Review Modal -->
    <Modal
      v-model:visible="showChangesModal"
      title="Import Changes Review"
      :width="1200"
      :footer="null"
    >
      <div v-if="changesData">
        <div style="margin-bottom: 16px">
          <h4>Summary:</h4>
          <div style="display: flex; gap: 24px">
            <span style="color: #52c41a">
              <strong>New Products:</strong> {{ changesData.newProducts.length }}
            </span>
            <span style="color: #faad14">
              <strong>Updated Products:</strong> {{ changesData.updatedProducts.length }}
            </span>
            <span style="color: #ff4d4f">
              <strong>Deleted Products:</strong> {{ changesData.deletedProducts.length }}
            </span>
          </div>
        </div>

        <ATabs v-model:activeKey="activeChangesTab">
          <ATabPane key="new" tab="New Products" :disabled="changesData.newProducts.length === 0">
            <div v-if="changesData.newProducts.length > 0">
              <ATable
                size="small"
                :columns="changesTableColumns"
                :data-source="changesData.newProducts"
                row-key="productCode"
                :pagination="{ pageSize: 10, size: 'small' }"
              />
            </div>
            <div v-else style="text-align: center; padding: 20px; color: #888">
              No new products to add
            </div>
          </ATabPane>

          <ATabPane
            key="updated"
            tab="Updated Products"
            :disabled="changesData.updatedProducts.length === 0"
          >
            <div v-if="changesData.updatedProducts.length > 0">
              <ATable
                size="small"
                :columns="changesTableColumnsWithDiff"
                :data-source="changesData.updatedProducts"
                row-key="productCode"
                :pagination="{ pageSize: 10, size: 'small' }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'changes'">
                    <div style="max-width: 300px">
                      <div
                        v-for="change in record.changes"
                        :key="change.field"
                        style="margin-bottom: 4px; font-size: 12px"
                      >
                        <strong>{{ change.field }}:</strong>
                        <div style="color: #ff4d4f">Old: {{ change.oldValue || '(empty)' }}</div>
                        <div style="color: #52c41a">New: {{ change.newValue || '(empty)' }}</div>
                      </div>
                    </div>
                  </template>
                </template>
              </ATable>
            </div>
            <div v-else style="text-align: center; padding: 20px; color: #888">
              No products to update
            </div>
          </ATabPane>

          <ATabPane
            key="deleted"
            tab="Deleted Products"
            :disabled="changesData.deletedProducts.length === 0"
          >
            <div v-if="changesData.deletedProducts.length > 0">
              <ATable
                size="small"
                :columns="changesTableColumns"
                :data-source="changesData.deletedProducts"
                row-key="productCode"
                :pagination="{ pageSize: 10, size: 'small' }"
              />
            </div>
            <div v-else style="text-align: center; padding: 20px; color: #888">
              No products to delete
            </div>
          </ATabPane>
        </ATabs>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  Modal,
  message,
  PageHeader,
  Select,
  Alert,
  Spin,
  Card as ACard,
  Form as AForm,
  FormItem as AFormItem,
  Empty as AEmpty,
  Row as ARow,
  Col as ACol,
  Table as ATable,
  Button as AButton,
  Space as ASpace,
  Tabs as ATabs,
  TabPane as ATabPane,
} from 'ant-design-vue'
import { CloseCircleFilled, WarningOutlined } from '@ant-design/icons-vue' // UploadOutlined removed as SVG is used
import { useRouter } from 'vue-router'

import type { Customer, CustomerSettings } from '@IVC/types/MasterDataTypes/Customer'
import type { ImportedProduct } from '@IVC/types/MasterDataTypes/Product'
import {
  getAllCustomers,
  getCustomerSettings,
  addOrUpdateCustomerSettings,
} from '@IVC/services/master-data/customer'
import {
  createImportBatch,
  upsertImportedProduct,
  findImportedProductByCodeAndCustomer,
  getAllImportedProductsByCustomer,
} from '@IVC/services/master-data/product'
import * as XLSX from 'xlsx'
import type { SelectValue } from 'ant-design-vue/lib/select'

interface ProductForPreview extends Omit<ImportedProduct, 'id' | 'importedAt' | 'importBatchId'> {
  hasDifferences?: boolean
  differencesDetail?: string[]
}

interface ProductChange {
  field: string
  oldValue: string
  newValue: string
}

interface ProductWithChanges extends ProductForPreview {
  changes?: ProductChange[]
}

interface ChangesData {
  newProducts: ProductForPreview[]
  updatedProducts: ProductWithChanges[]
  deletedProducts: ProductForPreview[]
}

// Define specific key types for better type safety
type WritableProductPreviewKeys =
  | 'productCode'
  | 'productName'
  | 'productPpcNum'
  | 'productHrc1'
  | 'productHrc3'
  | 'productPcm3'
type ComparableProductKeys = Extract<
  keyof ImportedProduct,
  'productName' | 'productPpcNum' | 'productHrc1' | 'productHrc3' | 'productPcm3'
>

// Interface for Ant Design Table columns
interface TableColumn {
  title: string
  dataIndex: string
  key: string
  width?: number | string
  ellipsis?: boolean | { showTitle?: boolean }
}

const router = useRouter()

const fileInputRef = ref<HTMLInputElement | null>(null)
const customers = ref<Customer[]>([])
const isCustomersLoading = ref(false)
const selectedCustomerId = ref<number | undefined>(undefined)
const customerSettings = ref<CustomerSettings | null>(null)
const fileSelected = ref<File | null>(null)
const isImporting = ref(false)
const isPreviewLoading = ref(false)
const isFileProcessed = ref(false) // Renamed from isPreviewAvailable
const productSamplesForPreview = ref<ProductForPreview[]>([])
const totalProductsToImport = ref(0)
const parsedDataForActualImport = ref<ProductForPreview[]>([])
const showDataDifferenceWarningModal = ref(false)
const previewTableColumns = ref<TableColumn[]>([])
const isDraggingOverDropZone = ref(false)
const rawExcelHeadersFromFile = ref<string[]>([])

// Column Mapping State (UI-facing: Excel Column Title -> DB Field Key)
const uiColumnMapping = ref<Record<string, string>>({})
const selectedExcelColumnForMapping = ref<string | null>(null)
const showSaveMappingModal = ref(false)
const isSavingMapping = ref(false)

const databaseProductFields = ref([
  { key: 'ProductCode', label: 'Product Code (Required)' }, // This is a DB field key
  { key: 'ProductName', label: 'Product Name' },
  { key: 'ProductPpcNum', label: 'Pieces Per Carton' },
  { key: 'ProductHrc1', label: 'Packaging Hierarchy' },
  { key: 'ProductHrc3', label: 'Product Category' },
  { key: 'ProductPcm3', label: 'Piece Cubic Meters' },
])

const showConfirmImportModal = ref(false)
const showPpcWarningModal = ref(false)

// Changes Modal State
const showChangesModal = ref(false)
const changesData = ref<ChangesData | null>(null)
const activeChangesTab = ref('new')

// Table columns for changes modal
const changesTableColumns = ref<TableColumn[]>([
  { title: 'Product Code', dataIndex: 'productCode', key: 'productCode', width: 120 },
  { title: 'Product Name', dataIndex: 'productName', key: 'productName', width: 200 },
  { title: 'PPC Number', dataIndex: 'productPpcNum', key: 'productPpcNum', width: 120 },
  { title: 'HRC1', dataIndex: 'productHrc1', key: 'productHrc1', width: 120 },
  { title: 'HRC3', dataIndex: 'productHrc3', key: 'productHrc3', width: 120 },
  { title: 'PCM3', dataIndex: 'productPcm3', key: 'productPcm3', width: 120 },
])

const changesTableColumnsWithDiff = ref<TableColumn[]>([
  { title: 'Product Code', dataIndex: 'productCode', key: 'productCode', width: 120 },
  { title: 'Product Name', dataIndex: 'productName', key: 'productName', width: 150 },
  { title: 'Changes', dataIndex: 'changes', key: 'changes', width: 300 },
])

const fetchCustomers = async () => {
  isCustomersLoading.value = true
  try {
    customers.value = await getAllCustomers()
  } catch (err) {
    message.error(`Failed to load customers: ${(err as Error).message}`)
  } finally {
    isCustomersLoading.value = false
  }
}

onMounted(async () => {
  await fetchCustomers()
  // Check for customerId in query params on initial load
  const queryCustomerId = router.currentRoute.value.query.customerId
  if (queryCustomerId) {
    const customerIdNum = Number(queryCustomerId)
    if (customers.value.some((c) => c.id === customerIdNum)) {
      await handleCustomerChange(customerIdNum) // Use await here
    }
  }
})

const resetPreviewStates = () => {
  isFileProcessed.value = false
  productSamplesForPreview.value = []
  totalProductsToImport.value = 0
  parsedDataForActualImport.value = []
  previewTableColumns.value = []
  showDataDifferenceWarningModal.value = false
  uiColumnMapping.value = {}
  rawExcelHeadersFromFile.value = []
  selectedExcelColumnForMapping.value = null
}

const handleCustomerChange = async (value: SelectValue) => {
  const customerId = value as number | undefined
  selectedCustomerId.value = customerId
  customerSettings.value = null
  fileSelected.value = null // Clear file if customer changes
  resetPreviewStates()

  if (customerId) {
    try {
      customerSettings.value = await getCustomerSettings(customerId)
      // Warning about unconfigured mapping is removed from here.
      // The user will interact with the mapping UI after file upload.
    } catch (err) {
      message.error(
        `Failed to load settings for customer ID ${customerId}: ${(err as Error).message}`,
      )
    }
  }
  // If a file is already selected, re-process it with the new customer's settings (or lack thereof)
  if (fileSelected.value) {
    await processFileForPreview(fileSelected.value)
  }
}

const loadUiMappingsFromCustomerSettings = () => {
  uiColumnMapping.value = {} // Reset current UI mapping
  // Use rawExcelHeadersFromFile to correctly map Excel Title to DB Key
  if (customerSettings.value?.masterDataImportMapping && rawExcelHeadersFromFile.value.length > 0) {
    const newUiMapping: Record<string, string> = {}
    for (const dbFieldKey in customerSettings.value.masterDataImportMapping) {
      const excelColumnLetter: string | undefined =
        customerSettings.value.masterDataImportMapping[dbFieldKey]
      if (excelColumnLetter) {
        // Ensure excelColumnLetter is a string
        const columnIndex = XLSX.utils.decode_col(excelColumnLetter)
        if (columnIndex < rawExcelHeadersFromFile.value.length) {
          const excelColumnTitleFromRawFile = rawExcelHeadersFromFile.value[columnIndex]
          newUiMapping[excelColumnTitleFromRawFile] = dbFieldKey
        }
      }
    }
    uiColumnMapping.value = newUiMapping
  }
}
const processFileForPreview = async (file: File) => {
  if (!selectedCustomerId.value) {
    message.error('Please select a customer before selecting a file for preview.')
    return
  }
  // Allow processing even if no saved mapping, user will map via UI.
  if (
    !customerSettings.value?.masterDataImportMapping ||
    Object.keys(customerSettings.value.masterDataImportMapping).length === 0
  ) {
    message.warn(
      // Changed to warn, as user can still map manually via UI.
      'No saved import mapping found for this customer. Please use the mapping interface below. Data preview will reflect current mappings.',
    )
    // Do NOT return; proceed to process the file for headers.
  }

  isPreviewLoading.value = true
  resetPreviewStates() // Ensure clean state before processing

  const currentCustomerId = selectedCustomerId.value
  const currentSettings = customerSettings.value

  const reader = new FileReader()
  reader.onload = async (e) => {
    try {
      const data = e.target?.result
      const workbook = XLSX.read(data, { type: 'array' })
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as (
        | string
        | number
        | boolean
        | Date
        | null
      )[][]

      if (jsonData.length < 1) {
        message.warn('Excel file seems empty.')
        isPreviewLoading.value = false
        return
      }

      const headers = jsonData[0].map((header) => String(header).trim())
      rawExcelHeadersFromFile.value = headers // Store all raw headers

      if (jsonData.length < 2) {
        message.warn('Excel file has headers but no data rows.')
        isPreviewLoading.value = false
        isFileProcessed.value = true
        if (headers.length > 0) {
          previewTableColumns.value = headers.map((header) => ({
            title: header,
            dataIndex: header.toLowerCase().replace(/\s+/g, '_'),
            key: header,
            width: 150,
            ellipsis: { showTitle: true },
          }))
        } // For mapping UI, but data table columns will be based on actual mapping
        return
      }

      const productsToSave: ProductForPreview[] = []
      let productsWithDifferencesCount = 0

      const dynamicColumns: TableColumn[] = []
      const mappedDbFields = new Set<string>()
      const excelLetterToDbFieldKey: Record<string, string> = {}
      if (currentSettings?.masterDataImportMapping) {
        for (const dbKey in currentSettings.masterDataImportMapping) {
          const letter = currentSettings.masterDataImportMapping[dbKey]
          if (letter) {
            excelLetterToDbFieldKey[letter.toUpperCase()] = dbKey
          }
        }
      }

      // Generate previewTableColumns for the data display table based on successfully mapped fields
      // This part ensures the data table only shows columns that are actually mapped and have data.
      headers.forEach((header, index) => {
        const excelColumnLetter = XLSX.utils.encode_col(index)
        const dbFieldKey = excelLetterToDbFieldKey[excelColumnLetter]
        if (dbFieldKey && !mappedDbFields.has(dbFieldKey)) {
          const targetKey = dbFieldKey.charAt(0).toLowerCase() + dbFieldKey.slice(1)
          dynamicColumns.push({
            title: header,
            dataIndex: targetKey,
            key: targetKey,
            width: 150,
            ellipsis: { showTitle: true },
          })
          mappedDbFields.add(dbFieldKey)
        }
      })
      previewTableColumns.value = dynamicColumns

      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i]
        const productFromExcel: ProductForPreview = {
          customerId: currentCustomerId,
          productCode: '',
          productName: '',
          productPpcNum: '',
          productHrc1: '',
          productHrc3: '',
          productPcm3: '',
          hasDifferences: false,
          differencesDetail: [],
        }

        let hasData = false
        if (currentSettings?.masterDataImportMapping) {
          for (const dbFieldKey in currentSettings.masterDataImportMapping) {
            const excelColumnLetter = currentSettings.masterDataImportMapping[dbFieldKey]
            if (excelColumnLetter) {
              const columnIndex = XLSX.utils.decode_col(excelColumnLetter)
              if (
                columnIndex < headers.length &&
                columnIndex < row.length &&
                row[columnIndex] !== undefined &&
                row[columnIndex] !== null &&
                String(row[columnIndex]).trim() !== ''
              ) {
                const targetKey = (dbFieldKey.charAt(0).toLowerCase() +
                  dbFieldKey.slice(1)) as WritableProductPreviewKeys
                productFromExcel[targetKey] = String(row[columnIndex])
                hasData = true
              }
            }
          }
        }

        if (hasData && productFromExcel.productCode && productFromExcel.productCode.trim() !== '') {
          const existingProductInDB = await findImportedProductByCodeAndCustomer(
            productFromExcel.productCode,
            currentCustomerId,
          )

          if (existingProductInDB) {
            const diffDetails: string[] = []
            const getExcelHeaderForDbField = (dbKeyToFind: string): string | undefined => {
              for (const col of previewTableColumns.value) {
                const expectedTargetKey = dbKeyToFind.charAt(0).toLowerCase() + dbKeyToFind.slice(1)
                if (col.dataIndex === expectedTargetKey) return col.title
              }
              return undefined
            }

            const fieldsToCompare: {
              dbKey: keyof CustomerSettings['masterDataImportMapping']
              productKey: ComparableProductKeys
            }[] = [
              { dbKey: 'ProductName', productKey: 'productName' },
              { dbKey: 'ProductPpcNum', productKey: 'productPpcNum' },
              { dbKey: 'ProductHrc1', productKey: 'productHrc1' },
              { dbKey: 'ProductHrc3', productKey: 'productHrc3' },
              { dbKey: 'ProductPcm3', productKey: 'productPcm3' },
            ]

            fieldsToCompare.forEach((field) => {
              const header = getExcelHeaderForDbField(field.dbKey as string)
              // Ensure productFromExcel[field.productKey] is treated as string for comparison, handling undefined
              const excelValue = String(productFromExcel[field.productKey] ?? '')
              const dbValue = String(existingProductInDB[field.productKey] ?? '')
              if (header && excelValue !== dbValue) {
                diffDetails.push(header)
              }
            })

            if (diffDetails.length > 0) {
              productFromExcel.hasDifferences = true
              productFromExcel.differencesDetail = diffDetails
              productsWithDifferencesCount++
            }
          }
          productsToSave.push(productFromExcel)
        }
      }

      parsedDataForActualImport.value = productsToSave
      totalProductsToImport.value = productsToSave.length
      productSamplesForPreview.value = productsToSave

      if (productsToSave.length > 0) {
        let successMessage = `${productsToSave.length} products parsed. Review before importing.`
        if (productsWithDifferencesCount > 0) {
          successMessage += ` ${productsWithDifferencesCount} product(s) have data different from the database.`
          showDataDifferenceWarningModal.value = true
        }
        message.info(successMessage)
      } else {
        message.warn(
          'No valid product data found in the file based on the mapping or product codes are missing.',
        )
        // If previewTableColumns is empty but rawExcelHeadersFromFile has headers,
        // it means no saved mapping matched. The mapping UI will still use rawExcelHeadersFromFile.
        // The data preview table will correctly be empty or show a message.
      }
    } catch (parseError) {
      message.error(`Error processing Excel file for preview: ${(parseError as Error).message}`)
      console.error('Excel parsing error for preview:', parseError)
    } finally {
      isPreviewLoading.value = false
      isFileProcessed.value = true
      loadUiMappingsFromCustomerSettings() // Attempt to load/apply saved mappings
    }
  }
  reader.onerror = (error) => {
    message.error('Failed to read file for preview.')
    console.error('File reading error for preview:', error)
    isPreviewLoading.value = false
    isFileProcessed.value = false
  }
  reader.readAsArrayBuffer(file)
}

const handleFileSelect = (event: Event) => {
  if (!selectedCustomerId.value) {
    message.error('Please select a customer before choosing a file.')
    if (fileInputRef.value) fileInputRef.value.value = '' // Reset file input
    return
  }
  const input = event.target as HTMLInputElement
  if (input.files && input.files.length > 0) {
    const file = input.files[0]
    fileSelected.value = file
    processFileForPreview(file)
  }
}

const handleFileDrop = (event: DragEvent) => {
  isDraggingOverDropZone.value = false
  if (!selectedCustomerId.value) {
    message.error('Please select a customer before dropping a file.')
    return
  }
  if (event.dataTransfer && event.dataTransfer.files.length > 0) {
    const file = event.dataTransfer.files[0]
    // Basic validation for Excel files by extension
    if (!file.name.match(/\.(xlsx|xls)$/i)) {
      message.error('Invalid file type. Please drop an Excel file (.xlsx or .xls).')
      return
    }
    fileSelected.value = file
    processFileForPreview(file)
  }
}

const handleRemoveFile = () => {
  fileSelected.value = null
  if (fileInputRef.value) fileInputRef.value.value = '' // Reset native file input
  resetPreviewStates()
}

const openImportConfirmationModal = () => {
  // Basic check: ensure product code is mapped if there are products to import
  const productCodeDbKey = 'ProductCode'
  const isProductCodeMapped = Object.values(uiColumnMapping.value).includes(productCodeDbKey)

  if (!selectedCustomerId.value) {
    message.error('Please select a customer first.')
    return
  }
  // Check if mapping is configured (either via saved settings or UI mapping)
  if (
    totalProductsToImport.value > 0 && // Only check mapping if there are products
    Object.keys(uiColumnMapping.value).length === 0 && // And no UI mapping is done
    (!customerSettings.value?.masterDataImportMapping ||
      Object.keys(customerSettings.value.masterDataImportMapping).length === 0) // And no saved mapping
  ) {
    message.warn(
      'Column mapping is not configured. Please map columns or save a mapping in Customer Settings.',
    )
    return
  }
  if (!parsedDataForActualImport.value || parsedDataForActualImport.value.length === 0) {
    message.error('No product data processed for import. Please select and preview a file.')
    return
  }
  if (
    totalProductsToImport.value > 0 &&
    !isProductCodeMapped &&
    !customerSettings.value?.masterDataImportMapping?.[productCodeDbKey]
  ) {
    message.error(
      `The "Product Code" field must be mapped before importing. Please check your column mappings or Customer Settings.`,
    )
    return
  }
  if (isPreviewLoading.value) {
    message.warn('File is still processing for preview. Please wait.')
    return
  }

  // Check for pieces per carton changes before showing import confirmation
  checkForPpcChanges()
}

const checkForPpcChanges = async () => {
  if (!selectedCustomerId.value || !parsedDataForActualImport.value.length) {
    showConfirmImportModal.value = true
    return
  }

  try {
    const productsWithPpcChanges: ProductWithChanges[] = []

    // Check each product from Excel file against database for PPC changes
    for (const product of parsedDataForActualImport.value) {
      const existingProduct = await findImportedProductByCodeAndCustomer(
        product.productCode,
        selectedCustomerId.value,
      )

      if (existingProduct) {
        // Check specifically for PPC Number changes (exclude new products and deletions)
        const newPpcValue = String(product.productPpcNum || '')
        const oldPpcValue = String(existingProduct.productPpcNum || '')

        if (newPpcValue !== oldPpcValue) {
          productsWithPpcChanges.push({
            ...product,
            changes: [
              {
                field: 'PPC Number',
                oldValue: oldPpcValue,
                newValue: newPpcValue,
              },
            ],
          })
        }
      }
    }

    if (productsWithPpcChanges.length > 0) {
      // Store the PPC changes data for the warning modal
      changesData.value = {
        newProducts: [],
        updatedProducts: productsWithPpcChanges,
        deletedProducts: [],
      }
      showPpcWarningModal.value = true
    } else {
      // No PPC changes, proceed directly to import confirmation
      showConfirmImportModal.value = true
    }
  } catch (error) {
    message.error(`Error checking for PPC changes: ${(error as Error).message}`)
    // Still show import confirmation even if check fails
    showConfirmImportModal.value = true
  }
}

const proceedWithImport = () => {
  showPpcWarningModal.value = false
  showConfirmImportModal.value = true
}

const executeImportProcessConfirmed = async (redirectAfterImport: boolean) => {
  isImporting.value = true
  try {
    const newBatch = await createImportBatch({
      sourceFileName: fileSelected.value?.name || 'Unknown File',
    })

    let insertedCount = 0
    let updatedCount = 0

    for (const prod of parsedDataForActualImport.value) {
      const result = await upsertImportedProduct({ ...prod, importBatchId: newBatch.id })
      if (result.operation === 'inserted') {
        insertedCount++
      } else if (result.operation === 'updated') {
        updatedCount++
      }
    }
    message.success(
      `Import complete for batch ID ${newBatch.id}. Products Inserted: ${insertedCount}, Updated: ${updatedCount}. Total processed: ${parsedDataForActualImport.value.length}.`,
    )

    fileSelected.value = null
    resetPreviewStates()
    if (fileInputRef.value) fileInputRef.value.value = ''

    if (redirectAfterImport && selectedCustomerId.value) {
      router.push({ name: 'product', query: { customerId: selectedCustomerId.value } })
    } else {
      // If not redirecting, the modal will close, and user stays on the page.
    }
  } catch (err) {
    message.error(`Import failed: ${(err as Error).message}`)
  } finally {
    showConfirmImportModal.value = false
    isImporting.value = false
  }
}

// --- Column Mapping UI Functions ---
const selectExcelColumnForMapping = (excelColTitle: string) => {
  if (selectedExcelColumnForMapping.value === excelColTitle) {
    selectedExcelColumnForMapping.value = null // Toggle off
  } else {
    selectedExcelColumnForMapping.value = excelColTitle
  }
}

const mapSelectedExcelColumnToDbField = (dbFieldKey: string) => {
  if (!selectedExcelColumnForMapping.value) {
    message.warn('Please select an Excel column first.')
    return
  }
  // If this DB field is already mapped by another Excel column, unmap that first
  for (const key in uiColumnMapping.value) {
    if (uiColumnMapping.value[key] === dbFieldKey && key !== selectedExcelColumnForMapping.value) {
      delete uiColumnMapping.value[key]
    }
  }
  uiColumnMapping.value[selectedExcelColumnForMapping.value] = dbFieldKey
  selectedExcelColumnForMapping.value = null // Reset selection
}

const clearUiMappingForExcelColumn = (excelColTitle: string) => {
  delete uiColumnMapping.value[excelColTitle]
  if (selectedExcelColumnForMapping.value === excelColTitle) {
    selectedExcelColumnForMapping.value = null
  }
}

const clearAllUiMappings = () => {
  uiColumnMapping.value = {}
  selectedExcelColumnForMapping.value = null
}

const openSaveMappingModal = () => {
  if (Object.keys(uiColumnMapping.value).length === 0) {
    message.warn('No mappings to save.')
    return
  }
  showSaveMappingModal.value = true
}

const saveCurrentUiMapping = async () => {
  if (!selectedCustomerId.value) return
  isSavingMapping.value = true
  const newMasterDataImportMapping: Record<string, string> = {}

  // Iterate over all headers found in the Excel file
  rawExcelHeadersFromFile.value.forEach((headerTitle, index) => {
    const dbFieldKey = uiColumnMapping.value[headerTitle] // Check if this header is mapped in the UI
    if (dbFieldKey) {
      // If mapped, store the DB Key against the Excel column letter
      newMasterDataImportMapping[dbFieldKey] = XLSX.utils.encode_col(index)
    }
  })

  await addOrUpdateCustomerSettings({
    customerId: selectedCustomerId.value,
    masterDataImportMapping: newMasterDataImportMapping,
  })
  customerSettings.value = await getCustomerSettings(selectedCustomerId.value) // Refresh local settings
  message.success('Column mapping saved successfully for this customer.')
  isSavingMapping.value = false
  showSaveMappingModal.value = false

  // Re-process the file with the new settings to update the preview
  if (fileSelected.value) {
    await processFileForPreview(fileSelected.value)
  }
}

const openChangesModal = async () => {
  if (!selectedCustomerId.value || !parsedDataForActualImport.value.length) {
    message.warn('No data available to show changes.')
    return
  }

  try {
    // Simulate analyzing changes by comparing with existing data
    const newProducts: ProductForPreview[] = []
    const updatedProducts: ProductWithChanges[] = []
    const deletedProducts: ProductForPreview[] = []

    // Get all existing products for this customer from database
    const existingProductsInDB = await getAllImportedProductsByCustomer(selectedCustomerId.value)

    // Create a Set of product codes from the Excel file for quick lookup
    const excelProductCodes = new Set(parsedDataForActualImport.value.map((p) => p.productCode))

    // Check each product from Excel file against database
    for (const product of parsedDataForActualImport.value) {
      const existingProduct = await findImportedProductByCodeAndCustomer(
        product.productCode,
        selectedCustomerId.value,
      )

      if (!existingProduct) {
        // New product
        newProducts.push(product)
      } else {
        // Check for updates
        const changes: ProductChange[] = []

        const fieldsToCheck: Array<{ key: ComparableProductKeys; label: string }> = [
          { key: 'productName', label: 'Product Name' },
          { key: 'productPpcNum', label: 'PPC Number' },
          { key: 'productHrc1', label: 'HRC1' },
          { key: 'productHrc3', label: 'HRC3' },
          { key: 'productPcm3', label: 'PCM3' },
        ]

        fieldsToCheck.forEach((field) => {
          const newValue = String(product[field.key] || '')
          const oldValue = String(existingProduct[field.key] || '')
          if (newValue !== oldValue) {
            changes.push({
              field: field.label,
              oldValue,
              newValue,
            })
          }
        })

        if (changes.length > 0) {
          updatedProducts.push({
            ...product,
            changes,
          })
        }
      }
    }

    // Check for deleted products: products that exist in DB but not in Excel file
    for (const existingProduct of existingProductsInDB) {
      if (!excelProductCodes.has(existingProduct.productCode)) {
        // This product exists in database but not in Excel file - it will be "deleted"
        deletedProducts.push({
          productCode: existingProduct.productCode,
          productName: existingProduct.productName,
          productPpcNum: existingProduct.productPpcNum,
          productHrc1: existingProduct.productHrc1,
          productHrc3: existingProduct.productHrc3,
          productPcm3: existingProduct.productPcm3,
          customerId: existingProduct.customerId,
        })
      }
    }

    changesData.value = {
      newProducts,
      updatedProducts,
      deletedProducts,
    }

    showChangesModal.value = true
  } catch (error) {
    message.error(`Error analyzing changes: ${(error as Error).message}`)
  }
}
</script>

<style scoped>
.row-has-differences td {
  background-color: #fff2e8;
}

.import-container-product {
  padding: 0 0 24px 0; /* Match ImportInbound's overall container padding if any, or keep it minimal */
  background: #fff;
  min-height: calc(100vh - 64px); /* Assuming 64px is header height */
}

.search-section-product {
  margin-bottom: 24px;
  padding: 0 16px;
}

.search-card-product {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.file-upload-product {
  margin-bottom: 20px;
  padding: 0 16px;
}

.file-metadata-product {
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  font-size: 14px;
}
.file-metadata-product h3 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
}
.file-metadata-product p {
  margin-bottom: 6px;
}

.drop-zone-product {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
  background-color: #fafafa;
  margin-bottom: 15px;
}
.drop-zone-product:hover,
.drop-zone-product.active {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.05);
}
.drop-message-product {
  color: #777;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-icon-product {
  margin-bottom: 15px;
  color: #1890ff;
}
.drop-message-product p {
  margin-bottom: 0;
  color: #555;
}
.drop-message-product small {
  color: #999;
  font-size: 0.85em;
}

.file-metadata-display-product {
  border: 1px solid #e8e8e8;
  padding: 16px;
  border-radius: 4px;
  background-color: #fafafa;
  margin-bottom: 15px;
}
.file-metadata-display-product h3 {
  margin-top: 0;
  margin-bottom: 12px;
}
.file-metadata-display-product p {
  margin-bottom: 6px;
}

.form-actions-product {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  margin-top: 16px;
}

.mapping-container-product {
  margin-top: 24px;
  margin-bottom: 30px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 20px;
  background-color: #fafafa;
  margin-left: 16px;
  margin-right: 16px;
}
.mapping-instructions {
  margin-bottom: 16px;
  color: #555;
}
.mapping-interface {
  display: flex;
  gap: 20px;
  justify-content: space-between;
}
.excel-columns-map-view,
.db-columns-map-view {
  flex: 1;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
}
.excel-columns-map-view h4,
.db-columns-map-view h4 {
  margin-top: 0;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  color: #333;
}
.column-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}
.column-item {
  padding: 8px 10px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}
.column-item-map:hover {
  background-color: #f5f5f5;
}
.column-item-map.active {
  background-color: #e6f7ff;
  font-weight: bold;
}
.column-item.excel-column:hover {
  background-color: #f0f7ff;
}
.column-item.db-column:hover:not(.disabled) {
  background-color: #f0fff0;
}
.column-item.active {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}
.column-item.mapped {
  color: #52c41a;
}
.column-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #fafafa;
}
.mapped-indicator {
  font-size: 0.9em;
  color: #52c41a;
  margin-left: 8px;
}
.clear-map-icon {
  margin-left: 6px;
  color: #ff4d4f;
}
.data-table-container-product {
  margin-top: 24px;
  padding: 0 16px;
  width: 100%;
  overflow-x: auto;
}
</style>
