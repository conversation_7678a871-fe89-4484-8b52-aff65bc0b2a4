export interface CustomerOutbound {
  id: number // PRIMARY KEY
  customerId: number // customer_id
  sprAsNum: string // spr_as_num
  sphShipYmd: string // sph_ship_ymd (ISO date: YYYY-MM-DD)
  sphDlvCod: number // sph_dlv_cod
  sphDlvNam1: string // sph_dlv_nam1
  sprProdCod: number // spr_prod_cod
  spdProdNam: string // spd_prod_nam
  sprRtpcQty: number // spr_rtpc_qty
  prodPpcNum: number // prod_ppc_num
  prodHrc1: string // prod_hrc1
  ai: number // ai
  innerMaster: number // inner_master
  carton: number // carton
  pcs: number // pcs
  innerCarton: number // inner_carton
  innerPcs: number // inner_pcs
  totalCarton: number // total_carton
  totalPcs: number // total_pcs
  totalAi: number // total_ai
  totalM3: number // total_m3
  createdAt: string // created_at (ISO datetime)
  updatedAt: string // updated_at (ISO datetime)
}
